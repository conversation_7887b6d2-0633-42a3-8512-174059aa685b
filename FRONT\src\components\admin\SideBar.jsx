import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import logo from '../../assets/images/logo/logo.png';
import { logoutApi } from '../../services/authService';
import { usePermissions } from '../../context/PermissionsContext';

function SideBar() {
  const location = useLocation();
  const navigate = useNavigate();

  const { permissions } = usePermissions();

  // State for toggle collapse in menu items
  const [collapsed, setCollapsed] = useState(false);
  // State for mobile sidebar visibility
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  // Track if we're on mobile or not
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Check screen size on mount and resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-hide sidebar when resizing to desktop
      if (window.innerWidth >= 768) {
        setShowMobileSidebar(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle mobile sidebar
  const toggleMobileSidebar = () => {
    setShowMobileSidebar(!showMobileSidebar);
  };

  const isActive = (path) => {
    // For courses, check if the current path starts with /admin/courses or contains analytics
    if (path === '/admin/courses') {
      return (location.pathname.startsWith('/admin/courses') || 
              location.pathname.includes('/analytics/') ||
              location.pathname.includes('CourseAnalyticsSurveyDetails') ||
              location.pathname.includes('CourseAnalyticsAssessmentDetails') ||
              location.pathname.includes('edit-create')) ? 'active' : '';
    }
    // For classroom, check if the path starts with /admin/classrooms
    if (path === '/admin/classrooms') {
      return (location.pathname.startsWith('/admin/classrooms') || 
              location.pathname.includes('classroom-dashboard') ||
              location.pathname.includes('ClassroomAssignmentDetails') ||
              location.pathname.includes('ClassroomAssessmentDetails') ||
              location.pathname.includes('/trainees/')) ? 'active' : '';
    }
    // For trainees, check if the path starts with /admin/trainees or includes traineesAnalytics
    if (path === '/admin/trainees') {
      return (location.pathname.startsWith('/admin/trainees') ||
              location.pathname.includes('traineesAnalytics')) ? 'active' : '';
    }
    // For other paths, exact match
    return location.pathname === path ? 'active' : '';
  };

  const handleLinkClick = () => {
    // Close sidebar on mobile when clicking a link
    if (isMobile) {
      setShowMobileSidebar(false);
    }
  };

  const handleLogout = async () => {
    try {
      // Optionally call logout API
      await logoutApi();

      // Clear all local storage items
      localStorage.clear();

      // Redirect to login page
      navigate('/');
    } catch (error) {
      console.error("Logout error:", error);
      localStorage.clear();
      navigate('/');
    }
  };

  // Mobile toggle button to be rendered in the layout
  const MobileToggleButton = () => (
    <button
      className="btn p-0"
      onClick={toggleMobileSidebar}
      aria-label="Toggle sidebar"
      style={{ border: 'none', background: 'none' }}
    >
      <Icon icon="mdi:menu" width="32" height="32" color="#212529" />
    </button>
  );

  // Standard sidebar content
  const SidebarContent = () => (
    <div className="sidebar-nav py-2">
      {permissions.dashboard_management_view && (
        <Link
          to="/admin/dashboard"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/dashboard') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:grid-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Dashboard</span>
        </Link>
      )}

      {permissions.course_management_view && (
        <Link
          to="/admin/courses"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/courses') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:book-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Courses</span>
        </Link>
      )}

      {permissions.classroom_management_view && (
        <Link
          to="/admin/classrooms"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/classrooms') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:video-person-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Classrooms</span>
        </Link>
      )}

      {permissions.course_approval_workflow_view && (
        <Link
          to="/admin/approvalRequest"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/approvalRequest') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:clipboard-task-list-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Approval Requests</span>
        </Link>
      )}

      {permissions.certificate_management_view && (
        <Link
          to="/admin/certificates"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/certificates') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:certificate-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Certificates</span>
        </Link>
      )}

      {permissions.trainee_management_view && (
        <Link
          to="/admin/trainees"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/trainees') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:people-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Trainees</span>
        </Link>
      )}

      {permissions.roles_and_access_user_view && (
        <Link
          to="/admin/roles-and-access"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/roles-and-access') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:shield-lock-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Roles & Access</span>
        </Link>
      )}

      {permissions.question_bank_management_view && (
        <Link
          to="/admin/question-bank"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/question-bank') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:book-question-mark-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Question Bank</span>
        </Link>
      )}


      {permissions.setting_management_view && (
        <Link
          to="/admin/settings"
          className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/admin/settings') ? 'active' : ''}`}
          onClick={handleLinkClick}
        >
          <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
            <Icon icon="fluent:settings-24-regular" width="22" height="22" />
          </div>
          <span className="nav-text">Settings</span>
        </Link>
      )}

      <div
        className="nav-item d-flex align-items-center py-2 px-3 text-danger mt-2"
        onClick={handleLogout}
        role="button"
        style={{ cursor: 'pointer' }}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:sign-out-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Logout</span>
      </div>
    </div>
  );

  return (
    <>
      {/* Logo for desktop view */}
      {!isMobile && (
        <div className="logo-container border-end" style={{ backgroundColor: '#f6f7f9', height: '92px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0.75rem' }}>
          <Link to="/admin/dashboard" className="text-decoration-none d-flex justify-content-center">
            <img
              src={logo} 
              alt="Logo"
              className="img-fluid"
              style={{ maxHeight: '100px', maxWidth: '100%' }}
            />
          </Link>
        </div>
      )}

      {/* Mobile Toggle Button - To be rendered in the layout */}
      {isMobile && <MobileToggleButton />}

      {/* Mobile Sidebar Overlay */}
      {showMobileSidebar && (
        <div
          className="d-md-none"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            zIndex: 1030
          }}
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Mobile Sidebar */}
      {isMobile && (
        <div
          className={`d-md-none mobile-sidebar border-end position-fixed ${showMobileSidebar ? 'show' : 'hide'}`}
          style={{
            width: '280px',
            height: '100vh',
            top: 0,
            left: 0,
            zIndex: 1040,
            transform: showMobileSidebar ? 'translateX(0)' : 'translateX(-100%)',
            transition: 'transform 0.3s ease-in-out'
          }}
        >
          {/* Mobile Logo */}
          <div className="logo-container border-bottom" style={{ height: '78px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0.75rem 1rem' }}>
            <Link to="/admin/dashboard" className="text-decoration-none">
              <img
                src={logo}
                alt="Logo"
                className="img-fluid"
                style={{ maxHeight: '50px', maxWidth: '100%' }}
              />
            </Link>
            <button
              className="btn btn-sm btn-outline-secondary rounded-circle"
              onClick={toggleMobileSidebar}
              aria-label="Close menu"
            >
              <Icon icon="fluent:dismiss-24-filled" width="20" height="20" />
            </button>
          </div>
          <div className="overflow-auto" style={{ height: 'calc(100vh - 78px)' }}>
            <SidebarContent />
          </div>
        </div>
      )}

      {/* Desktop Sidebar Content */}
      {!isMobile && (
        <div className="sidebar overflow-auto" style={{ minHeight: 'calc(100vh - 120px)' }}>
          <SidebarContent />
        </div>
      )}
    </>
  );
}

export default SideBar;