import React, { useState } from 'react';
import { Link, useNavigate, useLocation    } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import logo from '../../assets/images/logo/logo.png';
import { validatePassword, validateForm } from '../../utils/validation';
import { resetPasswordApi } from '../../services/authService';

function ResetPassword() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '' 
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const location = useLocation();
  const email = location.state?.email;
  const domain = window.location.origin;

  console.log("Reset Password Email:", email);
  // Password validation criteria
  const [validationCriteria, setValidationCriteria] = useState({
    hasEightChar: false,
    hasLetter: false,
    hasNumber: false,
    hasSpecialChar: false
  });

  // Check password criteria
  const checkPasswordCriteria = (password) => {
    setValidationCriteria({
      hasEightChar: password.length >= 8,
      hasLetter: /[a-zA-Z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    });
  };

  // Validation Rules
  const validationRules = {
    password: validatePassword,
    confirmPassword: (value) => {
      // First check if confirm password is empty
      if (!value) {
        return ['Confirm password is required'];
      }
      // Then check if it matches with password (after trimming both)
      if (value.trim() !== formData.password.trim()) {
        return ['Passwords do not match'];
      }
      return [];
    }
  };

  // Handle Change
  const handleChange = (e) => {
    const { name, value } = e.target;
  
    // Update form data
    const newFormData = { ...formData, [name]: value };

    console.log('Reset Password Data:', newFormData);
  
    if (name === 'password') {
      checkPasswordCriteria(value);
    }
  
    // Check password match whenever either password field changes
    if (newFormData.password && newFormData.confirmPassword) {
      // Trim both passwords to ensure no whitespace issues
      const password = newFormData.password.trim();
      const confirmPassword = newFormData.confirmPassword.trim();
  
      // If passwords match, remove error
      if (password === confirmPassword) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.confirmPassword; // Remove the error if passwords match
          return newErrors;
        });
      } else {
        setErrors(prev => ({
          ...prev,
          confirmPassword: ['Passwords do not match']
        }));
      }
    }
  
    setFormData(newFormData);
  };

  // Handle Submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Trim passwords before comparison
    const password = formData.password.trim();
    const confirmPassword = formData.confirmPassword.trim();

    // Validate passwords match first
    if (password !== confirmPassword) {
      setErrors(prev => ({
        ...prev,
        confirmPassword: ['Passwords do not match']
      }));
      return;
    }

    const validation = validateForm(formData, validationRules);
    if (validation.isValid) {
      setIsLoading(true);

      try {
        const payload = {
          email: email,
          newPassword: password,
          domain: domain
        };

        const response = await resetPasswordApi(payload);

        if (response.success) {
          toast.success('Password reset successfully!', {
            position: "top-center",
            autoClose: 2000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            onClose: () => {
              navigate('/auth/login');
            }
          });
        } else {
          throw new Error(response.message || 'Failed to reset password');
        }
      } catch (error) {
        toast.error(error.message || 'Failed to reset password. Please try again.', {
          position: "top-center",
          autoClose: 2000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true
        });
        setIsLoading(false);
      }
    } else {
      setErrors(validation.errors);
    }
  };

  return (
    <div className="auth-container">
      {/* Logo */}  
      <div className="auth-logo">
        <img src={logo} alt="LMS Logo" style={{ width: '180px', height: 'auto' }} />
      </div>

      {/* Reset Password Card */}
      <div className="auth-card">
        <div className="auth-header">
          <h2>Reset Password</h2>
          <p>Enter your new password</p>
        </div>

        {/* Reset Password Form */}
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-group">
            <label htmlFor="password">New Password</label>
            <div className={`auth-input-wrapper ${errors.password ? 'error' : ''}`}>
              <Icon icon="mdi:lock-outline" className="auth-input-icon" />
              <input 
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Enter new password" 
                className={`auth-input ${errors.password ? 'error' : ''}`}
                disabled={isLoading}
              />
              <button 
                type="button" 
                className="auth-password-toggle" 
                onClick={() => setShowPassword(!showPassword)}
                disabled={isLoading}
              >
                <Icon icon={showPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"} />
              </button>
            </div>
            {errors.password && <span className="auth-error-message">{errors.password[0]}</span>}
            
            {/* Password Requirements */}
            <div className="password-requirements">
              <div className={`requirement-badge ${validationCriteria.hasEightChar ? 'valid' : ''}`}>
                <Icon icon={validationCriteria.hasEightChar ? "mdi:check" : "mdi:close"} className="icon" />
                <span>8+ Characters</span>
              </div>
              <div className={`requirement-badge ${validationCriteria.hasLetter ? 'valid' : ''}`}>
                <Icon icon={validationCriteria.hasLetter ? "mdi:check" : "mdi:close"} className="icon" />
                <span>Letters</span>
              </div>
              <div className={`requirement-badge ${validationCriteria.hasNumber ? 'valid' : ''}`}>
                <Icon icon={validationCriteria.hasNumber ? "mdi:check" : "mdi:close"} className="icon" />
                <span>Numbers</span>
              </div>
              <div className={`requirement-badge ${validationCriteria.hasSpecialChar ? 'valid' : ''}`}>
                <Icon icon={validationCriteria.hasSpecialChar ? "mdi:check" : "mdi:close"} className="icon" />
                <span>Special Chars</span>
              </div>
            </div>
          </div>

          <div className="auth-form-group">
            <label htmlFor="confirmPassword">Confirm New Password</label>
            <div className={`auth-input-wrapper ${errors.confirmPassword ? 'error' : ''}`}>
              <Icon icon="mdi:lock-outline" className="auth-input-icon" />
              <input 
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm new password" 
                className={`auth-input ${errors.confirmPassword ? 'error' : ''}`}
                disabled={isLoading}
              />
              <button 
                type="button" 
                className="auth-password-toggle" 
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={isLoading}
              >
                <Icon icon={showConfirmPassword ? "mdi:eye-off-outline" : "mdi:eye-outline"} />
              </button>
            </div>
            {errors.confirmPassword && <span className="auth-error-message">{errors.confirmPassword[0]}</span>}
          </div>

          <button 
            type="submit" 
            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="spinner"></div>
                Resetting Password...
              </>
            ) : (
              'Reset Password'
            )}
          </button>
          
          <div className="auth-options">
            Remember your password? 
            <Link 
              to="/auth/login" 
              className={isLoading ? 'disabled-link' : ''}
            >
              Login here
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ResetPassword;