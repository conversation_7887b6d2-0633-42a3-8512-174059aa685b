const { mysqlServerConnection } = require("../../../db/db");
const axios = require("axios");
const { deleteFileIfExists, LogsHandler } = require("../../../tools/tools");
const {
  sentEmailNotification,
  sendLiveClassEmail,
} = require("../../../tools/emailtemplates");
const { SetNotification } = require("../../../utils/sendNotificaiton");
const { uploadImageToS3 } = require("../../../tools/aws");
const API_KEY = "0i1znp1qQA6kXyVx1PjtGw";
const API_SECRET = "Q6TWyo5CQSSKUvebUYsVSg";
const clientId = "JsGzsmFoT2nFom9GWcdfw";
const clientSecret = "JM36as2IhbRxpmb3gEN0BXOTV0mxWLVj";
const redirectUri = "https://localhost:5000";
const { formatDateTimeForMySQL } = require("../../../utils/dateFormat");

const getAllClasses = async (req, res, next) => {
  console.log("▶️ getAllClasses API called");

  try {
    const { page = 1, limit = 10, search = "" } = req.body;
    const dbName = req.user.db_name;
    const trainerUserId = req.body.user_id;
    const offset = (page - 1) * limit;

    console.log("req.body", req.body);

    console.log("🧑 User ID:", trainerUserId);
    console.log("🏢 Database:", dbName);

    let isAdmin = false;
    const [role] = await mysqlServerConnection.query(
      `SELECT r.name FROM ${dbName}.user_roles ur 
       JOIN ${dbName}.roles r ON ur.role_id = r.id 
       WHERE ur.user_id = ?`,
      [trainerUserId]
    );

    if (role[0]?.name === "admin") {
      isAdmin = true;
    }

    let classroomQuery = "";
    let queryParams = [];
    let countQuery = "";
    let countParams = [];

    if (!isAdmin) {
      classroomQuery = `
        SELECT
          c.id,
          c.cls_name,
          c.cls_desc,
          c.collaboration_name,
          c.banner_img,
          c.is_active,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_trainee ct
            WHERE ct.class_id = c.id
          ) AS total_trainees,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_trainer ctr
            JOIN ${dbName}.user_roles ur ON ctr.user_id = ur.user_id
            JOIN ${dbName}.roles r ON ur.role_id = r.id
            WHERE ctr.classroom_id = c.id AND r.name LIKE '%trainer%'
          ) AS total_trainers,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_assignments ca
            WHERE ca.class_id = c.id AND ca.is_deleted = 0
          ) AS total_assignments,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_assessments cas
            WHERE cas.class_id = c.id AND cas.is_deleted = 0
          ) AS total_assessments,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_resources cr
            WHERE cr.classroom_id = c.id AND cr.is_deleted = 0 AND cr.resource_type IN ('video', 'audio')
          ) AS total_recorded,
          (
            SELECT COUNT(*) FROM ${dbName}.live_class lc
            WHERE lc.class_id = c.id AND lc.is_deleted = 0
          ) AS total_live_classes,
          c.createdAt
        FROM ${dbName}.classroom c
        JOIN ${dbName}.classroom_trainer ct_map ON ct_map.classroom_id = c.id
        WHERE c.is_deleted = 0 AND ct_map.user_id = ?
      `;
      queryParams = [trainerUserId];

      countQuery = `
        SELECT COUNT(DISTINCT c.id) AS total
        FROM ${dbName}.classroom c
        JOIN ${dbName}.classroom_trainer ct_map ON ct_map.classroom_id = c.id
        WHERE c.is_deleted = 0 AND ct_map.user_id = ?
      `;
      countParams = [trainerUserId];
    } else {
      classroomQuery = `
        SELECT
          c.id,
          c.cls_name,
          c.cls_desc,
          c.collaboration_name,
          c.banner_img,
          c.is_active,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_trainee ct
            WHERE ct.class_id = c.id
          ) AS total_trainees,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_trainer ctr
            JOIN ${dbName}.user_roles ur ON ctr.user_id = ur.user_id
            JOIN ${dbName}.roles r ON ur.role_id = r.id
            WHERE ctr.classroom_id = c.id AND r.name LIKE '%trainer%'
          ) AS total_trainers,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_assignments ca
            WHERE ca.class_id = c.id AND ca.is_deleted = 0
          ) AS total_assignments,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_assessments cas
            WHERE cas.class_id = c.id AND cas.is_deleted = 0
          ) AS total_assessments,
          (
            SELECT COUNT(*) FROM ${dbName}.classroom_resources cr
            WHERE cr.classroom_id = c.id AND cr.is_deleted = 0 AND cr.resource_type IN ('video', 'audio')
          ) AS total_recorded,
          (
            SELECT COUNT(*) FROM ${dbName}.live_class lc
            WHERE lc.class_id = c.id AND lc.is_deleted = 0
          ) AS total_live_classes,
          c.createdAt
        FROM ${dbName}.classroom c
        WHERE c.is_deleted = 0
      `;
      queryParams = [];

      countQuery = `
        SELECT COUNT(DISTINCT c.id) AS total
        FROM ${dbName}.classroom c
        WHERE c.is_deleted = 0
      `;
      countParams = [];
    }

    // Search filter
    if (search) {
      classroomQuery += ` AND c.cls_name LIKE ?`;
      queryParams.push(`%${search}%`);
      countQuery += ` AND c.cls_name LIKE ?`;
      countParams.push(`%${search}%`);
    }

    classroomQuery += ` ORDER BY c.createdAt DESC LIMIT ? OFFSET ?`;
    queryParams.push(Number(limit), offset);

    const [classes] = await mysqlServerConnection.query(
      classroomQuery,
      queryParams
    );
    const [totalResults] = await mysqlServerConnection.query(
      countQuery,
      countParams
    );

    const totalRecords = totalResults[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    const classroomIds = classes.map((cls) => cls.id);
    let trainersMap = {};

    if (classroomIds.length > 0) {
      const [trainersData] = await mysqlServerConnection.query(
        `
        SELECT
          ct.classroom_id,
          u.id AS user_id,
          u.name,
          u.email,
          u.profile_pic_url,
          r.name AS role_name
        FROM ${dbName}.classroom_trainer ct
        JOIN ${dbName}.users u ON u.id = ct.user_id
        JOIN ${dbName}.user_roles ur ON ur.user_id = u.id
        JOIN ${dbName}.roles r ON r.id = ur.role_id
        WHERE ct.classroom_id IN (?) AND r.name LIKE '%trainer%'
        `,
        [classroomIds]
      );

      trainersData.forEach((trainer) => {
        if (!trainersMap[trainer.classroom_id]) {
          trainersMap[trainer.classroom_id] = [];
        }
        trainersMap[trainer.classroom_id].push({
          user_id: trainer.user_id,
          name: trainer.name,
          email: trainer.email,
          profile_pic_url: trainer.profile_pic_url,
          role_name: trainer.role_name,
        });
      });
    }

    const enrichedClasses = classes.map((cls) => ({
      ...cls,
      trainers: trainersMap[cls.id] || [],
    }));

    enrichedClasses.forEach((cls) => {
      console.log(
        `📘 ${cls.cls_name} | Trainees: ${cls.total_trainees} | Trainers: ${cls.total_trainers} | Assignments: ${cls.total_assignments} | Assessments: ${cls.total_assessments} | Recorded: ${cls.total_recorded} | Live Classes: ${cls.total_live_classes}`
      );
    });

    return res.status(200).json({
      success: true,
      data: {
        classes: enrichedClasses,
        message: "Classes fetched successfully",
        status: 200,
        pagination: {
          currentPage: Number(page),
          limit: Number(limit),
          totalPages,
          totalRecords,
        },
      },
    });
  } catch (error) {
    console.error("❌ Error fetching classrooms:", error);
    return next({
      status: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// Abid Modified
// Abid Modified - Get All Classes API for Trainer
// const getAllClasses = async (req, res, next) => {
//   console.log("getAllClasses");

//   try {
//     // Extract pagination and search term
//     const { page = 1, limit = 10, search = "" } = req.body;
//     const dbName = req.user.db_name; // Database name for this user
//     const trainerUserId = req.user.userId; // Logged-in trainer ID
//     const offset = (page - 1) * limit;

//     console.log("User ID:", trainerUserId);
//     console.log("Database:", dbName);

//     // SQL query to fetch class info where current user is a trainer
//     let classroomQuery = `
//       SELECT DISTINCT
//         c.id,
//         c.cls_name,
//         c.cls_desc,
//         c.collaboration_name,
//         c.banner_img,
//         c.createdAt  -- Needed for ORDER BY with DISTINCT
//       FROM ${dbName}.classroom c
//       JOIN ${dbName}.classroom_trainer ct_map
//         ON ct_map.classroom_id = c.id
//       WHERE c.is_deleted = 0
//         AND ct_map.user_id = ?
//     `;

//     const params = [trainerUserId];

//     // Apply search filter if provided
//     if (search) {
//       classroomQuery += ` AND (c.cls_name LIKE ?)`;
//       params.push(`%${search}%`);
//     }

//     // Append ORDER BY and pagination
//     classroomQuery += ` ORDER BY c.createdAt DESC LIMIT ? OFFSET ?`;
//     params.push(Number(limit), offset);

//     // Execute classroom query
//     const [classes] = await mysqlServerConnection.query(classroomQuery, params);
//     console.log("Fetched classrooms data:", classes);

//     // Count total classrooms (for pagination)
//     let countQuery = `
//       SELECT COUNT(DISTINCT c.id) AS total
//       FROM ${dbName}.classroom c
//       JOIN ${dbName}.classroom_trainer ct_map
//         ON ct_map.classroom_id = c.id
//       WHERE c.is_deleted = 0
//         AND ct_map.user_id = ?
//     `;
//     const countParams = [trainerUserId];

//     // Apply same search filter to count query
//     if (search) {
//       countQuery += ` AND (c.cls_name LIKE ?)`;
//       countParams.push(`%${search}%`);
//     }

//     const [totalResults] = await mysqlServerConnection.query(countQuery, countParams);
//     const totalRecords = totalResults[0].total;
//     const totalPages = Math.ceil(totalRecords / limit);

//     console.log("Pagination:", { totalRecords, totalPages, page, limit });

//     // Return final JSON response
//     return res.status(200).json({
//       success: true,
//       data: {
//         classes,
//         message: "Classes fetched successfully",
//         status: 200,
//         pagination: {
//           currentPage: Number(page),
//           limit: Number(limit),
//           totalPages,
//           totalRecords
//         }
//       }
//     });

//   } catch (error) {
//     console.error("Error fetching all classes:", error);
//     return next({
//       statusCode: 500,
//       message: error.message || "Internal Server Error",
//     });
//   }
// };

const activeDeactiveClassroom = async (req, res, next) => {
  try {
    const { class_id, status } = req.body;
    const dbName = req.user.db_name;

    // Validate required fields
    if (!class_id || status === undefined) {
      return res.status(400).json({
        success: false,
        message: "Class ID and status are required",
      });
    }

    // Check if classroom exists
    const [classExists] = await mysqlServerConnection.query(
      `SELECT id, cls_name FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [class_id]
    );

    if (classExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or has been deleted",
      });
    }

    // Update classroom active status
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom SET is_active = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?`,
      [status ? 1 : 0, class_id]
    );

    const statusMessage = status ? "activated" : "deactivated";

    return res.status(200).json({
      success: true,
      status: 200,
      message: `Classroom has been ${statusMessage} successfully`,
    });
  } catch (error) {
    console.error("Error updating classroom status:", error);
    return res.status(500).json({
      success: false,
      status: 500,
      message: "An error occurred while updating classroom status",
      error: error.message,
    });
  }
};

const getAllStudentApproval = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const [totalResults] = await mysqlServerConnection.query(
      `SELECT COUNT(*) AS total FROM ${req.user.db_name}.classroom_approval`
    );
    const totalRecords = totalResults[0].total;

    const [pendingList] = await mysqlServerConnection.query(
      `SELECT ca.*,u.name,c.cls_name from
            ${req.user.db_name}.classroom_approval ca join 
            ${req.user.db_name}.users u on ca.user_id = u.id
            join ${req.user.db_name}.classroom c on ca.classroom_id = c.id
            where ca.is_approval IS NULL
            LIMIT ? OFFSET ?
        `,
      [limit, offset]
    );

    // console.log('call', pendingList)
    const totalPages = Math.ceil(totalRecords / limit);

    res.status(200).json({
      success: true,
      data: pendingList,
      pagination: {
        page,
        limit,
        totalPages: totalPages,
        totalCount: totalResults,
      },
    });
  } catch (error) {
    console.log(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const approveOrDenyClassroom = async (req, res, next) => {
  try {
    const { classroom_id, action, user_id } = req.query;

    // Validate input
    if (!classroom_id || !action || !user_id) {
      return res.status(400).json({
        success: false,
        error_msg:
          "Please provide classroom ID, user ID, and action (approve/deny)",
      });
    }

    const [existingClassroom] = await mysqlServerConnection.query(
      `SELECT id FROM ${req.user.db_name}.classroom_approval WHERE classroom_id = ?`,
      [classroom_id]
    );

    if (existingClassroom.length === 0) {
      return res.status(400).json({
        success: false,
        error_msg: "Classroom does not exist or does not belong to the user.",
      });
    }

    // Update classroom approval status for the specified user

    if (action === "approve") {
      await mysqlServerConnection.query(
        `UPDATE  ${req.user.db_name}.classroom_approval SET is_approval = ? where classroom_id = ? AND user_id=?`,
        [1, classroom_id, user_id]
      );

      await SetNotification({
        dbName: req.user.db_name,
        userId: user_id,
        title: `Your class room request has been approved`,
        body: ` Hi, thank you for submitting your classroom request. Your request has been approved by ${req.user.name}. You can now access the classroom.`,
      });
      LogsHandler(
        req.user.db_name,
        req.user.userId,
        "Request approved",
        "Class room approval request accepted"
      );
      res.status(200).json({
        success: true,
        message: `Classroom ${action}d successfully for user ID ${user_id}`,
      });
    } else if (action === "deny") {
      await SetNotification({
        dbName: req.user.db_name,
        userId: user_id,
        title: `Your class room request has been denied`,
        body: ` Hi, thank you for submitting your classroom request. Currently we cannot approve your classroom request. Your request has been denied.`,
      });
      LogsHandler(
        req.user.db_name,
        req.user.userId,
        "Request denied",
        "Class room approval request Denied"
      );

      await mysqlServerConnection.query(
        `UPDATE  ${req.user.db_name}.classroom_approval SET is_approval = ? where classroom_id = ? AND user_id=?`,
        [0, classroom_id, user_id]
      );
    } else {
      return res.status(400).json({
        success: false,
        error_msg: "Invalid action. Use 'approve' or 'deny'.",
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      success: false,
      error_msg: error.message || "Internal Server Error",
    });
  }
};

const deleteClassRoom = async (req, res, next) => {
  console.log("deleteClassRoom");
  try {
    const { classroom_id } = req.params;
    const dbName = req.user.db_name;

    console.log("DELETE CLASSROOM id", classroom_id);

    if (!classroom_id) {
      return res.status(400).json({
        state: 400,
        success: false,
        message: "Classroom ID is required",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom SET is_deleted = 1 where id = ?`,
      [classroom_id]
    );
    if (result.affectedRows == 0) {
      return res.status(404).json({
        state: 404,
        success: false,
        message: "Classroom not found",
      });
    }
    console.log(result);
    res.status(200).json({
      state: 200,
      success: true,
      message: "Classroom deleted successfully",
    });
  } catch (error) {
    return res.status(500).json({
      state: 500,
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

// Abid Modified
// Abid Modified
const createClassroom = async (req, res, next) => {
  console.log("createClassroom-----------", req.body);
  try {
    const { cls_name, cls_desc, collaboration_name, trainers } = req.body;
    console.log("cls_name", cls_name);
    console.log("cls_desc", cls_desc);
    console.log("collaboration_name", collaboration_name);
    console.log("trainers------", trainers);
    const user_id = req.user.userId;
    const dbName = req.user.db_name;
    const file = req.file;

    let url = "";
    if (!file) {
      return res.status(400).json({
        success: false,
        error_msg: "Banner image required",
      });
    } else {
      const path = await uploadImageToS3(file.buffer);
      url = path.path;
    }

    // Insert classroom
    const [classroomResult] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.classroom (user_id, cls_name, cls_desc, banner_img, collaboration_name, is_active)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [user_id, cls_name, cls_desc, url, collaboration_name, 1]
    );

    const classroom_id = classroomResult.insertId;

    // Create group room
    const [group] = await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.group_rooms (group_name, created_by, class_id) VALUES (?, ?, ?)`,
      [collaboration_name || cls_name, user_id, classroom_id]
    );
    const group_id = group.insertId;

    // Add current user to group members
    await mysqlServerConnection.query(
      `INSERT INTO ${dbName}.group_members (group_id, user_id) VALUES (?, ?)`,
      [group_id, user_id]
    );

    // ✅ Trainer logic — only passed trainers + admin if current user is one
    let trainerIds = [];

    if (trainers) {
      const parsedTrainers = JSON.parse(trainers);
      if (Array.isArray(parsedTrainers)) {
        trainerIds = parsedTrainers.map((t) => t.user_id);
      }
    }

    // Check if current user is admin (role_id = 1)
    const [isAdminResult] = await mysqlServerConnection.query(
      `SELECT 1 FROM ${dbName}.user_roles WHERE user_id = ? AND role_id = 1`,
      [user_id]
    );

    if (isAdminResult.length > 0) {
      trainerIds.push(user_id);
    }

    const uniqueTrainerIds = [...new Set(trainerIds)];

    // Bulk insert
    if (uniqueTrainerIds.length > 0) {
      const insertValues = uniqueTrainerIds.map((userId) => [
        classroom_id,
        userId,
        new Date(),
        new Date(),
      ]);

      await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.classroom_trainer (classroom_id, user_id, created_at, updated_at) VALUES ?`,
        [insertValues]
      );
    }

    res.status(200).json({
      success: true,
      message: "Classroom created successfully",
    });
  } catch (error) {
    console.error("Error while creating classroom:", error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};



// Abid Modified
// Abid Modified with Debug Logs
// Abid Modified — Updated to check trainer access instead of creator
const updateClassroom = async (req, res, next) => {
  console.log("📦 Updating classroom:", req.body);
  const dbName = req.user.db_name;
  console.log("🏢 Database name:", dbName);

  try {
    const {
      user_id,
      cls_name,
      cls_desc,
      collaboration_name,
      class_id,
      trainers,
    } = req.body;

    if (!/^[a-zA-Z0-9_]+$/.test(dbName)) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid database name" });
    }

    if (!class_id) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Class ID is required",
      });
    }

    // First check if user is an admin
    const [userRole] = await mysqlServerConnection.query(
      `SELECT r.name 
       FROM ${dbName}.user_roles ur 
       JOIN ${dbName}.roles r ON ur.role_id = r.id 
       WHERE ur.user_id = ?`,
      [user_id]
    );

    const isAdmin = userRole.length > 0 && userRole[0].name === "admin";

    // If not admin, check if user is a trainer in this class
    if (!isAdmin) {
      const [data] = await mysqlServerConnection.query(
        `SELECT c.* 
         FROM ${dbName}.classroom c
         JOIN ${dbName}.classroom_trainer ct ON ct.classroom_id = c.id
         WHERE c.id = ? AND ct.user_id = ? AND c.is_deleted = false`,
        [class_id, user_id]
      );

      console.log("🧾 Existing classroom data:", data);

      if (data.length === 0) {
        return res.status(404).json({
          success: false,
          status: 404,
          message: "Classroom not found or not assigned to user.",
        });
      }
    }

    // Get classroom data regardless of user role
    const [classroomData] = await mysqlServerConnection.query(
      `SELECT * FROM ${dbName}.classroom WHERE id = ? AND is_deleted = false`,
      [class_id]
    );

    if (classroomData.length === 0) {
      return res.status(404).json({
        success: false,
        status: 404,
        message: "Classroom not found.",
      });
    }

    // Banner image logic
    let imageUrl = classroomData[0].banner_img;
    if (req.file) {
      console.log("📤 Uploading new banner image...");
      const path = await uploadImageToS3(req.file.buffer);
      imageUrl = path.path;
      console.log("✅ Uploaded image URL:", imageUrl);
    }

    // Update classroom info
    console.log("🛠️ Updating classroom info...");
    await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom 
       SET cls_name = ?, cls_desc = ?, collaboration_name = ?, banner_img = ?, updatedAt = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [cls_name, cls_desc, collaboration_name, imageUrl, class_id]
    );

    // Update group name if changed
    if (collaboration_name !== classroomData[0].collaboration_name) {
      console.log("🔄 Updating group name...");
      await mysqlServerConnection.query(
        `UPDATE ${dbName}.group_rooms SET group_name = ? WHERE class_id = ?`,
        [collaboration_name, class_id]
      );
    }

    // Parse and update trainers
    let parsedTrainers = [];
    try {
      parsedTrainers = JSON.parse(trainers || "[]");
      console.log("👥 Parsed trainers:", parsedTrainers);
    } catch (e) {
      return res.status(400).json({
        success: false,
        status: 400,
        message: "Invalid trainers format",
      });
    }

    const [existingTrainers] = await mysqlServerConnection.query(
      `SELECT ct.user_id, ur.role_id, r.name AS role_name
       FROM ${dbName}.classroom_trainer ct
       JOIN ${dbName}.user_roles ur ON ct.user_id = ur.user_id
       JOIN ${dbName}.roles r ON ur.role_id = r.id
       WHERE ct.classroom_id = ?`,
      [class_id]
    );

    const incomingTrainerIds = parsedTrainers.map((t) => t.user_id);

    // Remove trainers not in list, excluding super admin (role_id = 1)
    const trainersToDelete = existingTrainers
      .filter((t) => !incomingTrainerIds.includes(t.user_id) && t.role_id !== 1)
      .map((t) => t.user_id);

    if (trainersToDelete.length > 0) {
      console.log("🗑️ Removing trainers:", trainersToDelete);
      await mysqlServerConnection.query(
        `DELETE FROM ${dbName}.classroom_trainer 
         WHERE classroom_id = ? AND user_id IN (?)`,
        [class_id, trainersToDelete]
      );
    }

    // Add new trainers
    const existingTrainerIds = existingTrainers.map((t) => t.user_id);
    const newTrainerEntries = parsedTrainers.filter(
      (t) => !existingTrainerIds.includes(t.user_id)
    );

    if (newTrainerEntries.length > 0) {
      console.log("➕ Adding new trainers:", newTrainerEntries);
      const insertValues = newTrainerEntries.map((trainer) => [
        class_id,
        trainer.user_id,
        new Date(),
        new Date(),
      ]);

      await mysqlServerConnection.query(
        `INSERT INTO ${dbName}.classroom_trainer 
         (classroom_id, user_id, created_at, updated_at) VALUES ?`,
        [insertValues]
      );
    }

    console.log("✅ Classroom updated successfully.");
    return res.status(200).json({
      success: true,
      status: 200,
      message: "Classroom details updated successfully",
    });
  } catch (error) {
    console.error("❌ Error updating classroom:", error);
    return res.status(500).json({
      success: false,
      status: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// ✅ 2. Any Assigned Trainer Can Update
const updateClassroom_TrainerAllowed = async (req, res, next) => {
  const dbName = req.user.db_name;
  const { user_id, class_id } = req.body;

  try {
    const [data] = await mysqlServerConnection.query(
      `SELECT c.* FROM ${dbName}.classroom c
       JOIN ${dbName}.classroom_trainer ct ON ct.classroom_id = c.id
       WHERE c.id = ? AND ct.user_id = ? AND c.is_deleted = false`,
      [class_id, user_id]
    );

    if (data.length === 0) {
      return res
        .status(404)
        .json({ success: false, message: "Not a trainer for this classroom" });
    }

    return res
      .status(200)
      .json({ success: true, message: "Trainer authorized" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

// ✅ 3. Creator or Admin (role_id = 1) Can Update
const updateClassroom_CreatorOrAdmin = async (req, res, next) => {
  const dbName = req.user.db_name;
  const { user_id, class_id } = req.body;

  try {
    const [data] = await mysqlServerConnection.query(
      `SELECT c.* FROM ${dbName}.classroom c
       JOIN ${dbName}.user_roles ur ON ur.user_id = ?
       WHERE c.id = ? AND (c.user_id = ? OR ur.role_id = 1) AND c.is_deleted = false`,
      [user_id, class_id, user_id]
    );

    if (data.length === 0) {
      return res
        .status(403)
        .json({ success: false, message: "Not authorized" });
    }

    return res
      .status(200)
      .json({ success: true, message: "Authorized as creator or admin" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

// ✅ 4. Creator or Trainer (excluding Super Admin)
const updateClassroom_CreatorOrNonSuperTrainer = async (req, res, next) => {
  const dbName = req.user.db_name;
  const { user_id, class_id } = req.body;

  try {
    const [data] = await mysqlServerConnection.query(
      `SELECT c.* FROM ${dbName}.classroom c
       JOIN ${dbName}.classroom_trainer ct ON ct.classroom_id = c.id
       JOIN ${dbName}.user_roles ur ON ur.user_id = ct.user_id
       WHERE c.id = ? AND ct.user_id = ? AND ur.role_id != 1 AND c.is_deleted = false`,
      [class_id, user_id]
    );

    if (data.length === 0) {
      return res
        .status(403)
        .json({ success: false, message: "Not authorized or is super admin" });
    }

    return res
      .status(200)
      .json({ success: true, message: "Trainer or creator authorized" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

// ✅ 5. Only Admin (role_id = 1) Can Update
const updateClassroom_AdminOnly = async (req, res, next) => {
  const dbName = req.user.db_name;
  const { user_id, class_id } = req.body;

  try {
    const [data] = await mysqlServerConnection.query(
      `SELECT c.* FROM ${dbName}.classroom c
       JOIN ${dbName}.user_roles ur ON ur.user_id = ?
       WHERE c.id = ? AND ur.role_id = 1 AND c.is_deleted = false`,
      [user_id, class_id]
    );

    if (data.length === 0) {
      return res
        .status(403)
        .json({
          success: false,
          message: "Only admins can update this classroom",
        });
    }

    return res.status(200).json({ success: true, message: "Admin authorized" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

const createLiveClass = async (req, res, next) => {
  try {
    const { className, description, date, end_time, start_time, class_id } =
      req.body;
    console.log("CREATE LIVE CLASS", req.body);

    // Validate date is not in the past
    const today = new Date();
    const selectedDate = new Date(date);
    selectedDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return res.status(400).json({
        success: false,
        message: "The selected date should not be less than today's date.",
      });
    }

    // Validate start time is not in the past for today's classes
    const selectedStartDateTime = new Date(start_time);
    const currentDateTime = new Date();

    if (
      selectedDate.getTime() === today.getTime() &&
      selectedStartDateTime < currentDateTime
    ) {
      return res.status(400).json({
        success: false,
        message: "The start time cannot be in the past for today's classes.",
      });
    }

    // Validate end time is after start time
    const selectedEndDateTime = new Date(end_time);
    if (selectedEndDateTime <= selectedStartDateTime) {
      return res.status(400).json({
        success: false,
        message: "The end time should be after the start time.",
      });
    }

    // Validate class duration is not more than 24 hours
    const durationHours =
      (selectedEndDateTime - selectedStartDateTime) / (1000 * 60 * 60);
    if (durationHours > 24) {
      return res.status(400).json({
        success: false,
        message: "Class duration cannot exceed 24 hours.",
      });
    }

    const [result] = await mysqlServerConnection.query(
      `INSERT INTO ${req.user.db_name}.live_class (class_name, class_desc, class_date, start_time, end_time, class_id, created_at, updated_at, is_active, is_deleted) 
      VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false, false)`,
      [
        className,
        description || null,
        date,
        formatDateTimeForMySQL(start_time),
        formatDateTimeForMySQL(end_time),
        class_id,
      ]
    );

    // Fetch all trainees from the classroom_trainee table
    const [userMails] = await mysqlServerConnection.query(
      `
        SELECT u.email, u.id 
        FROM ${req.user.db_name}.classroom_trainee ct
        JOIN ${req.user.db_name}.users u ON ct.user_id = u.id
        WHERE ct.class_id = ? AND u.is_blocked = 0 AND u.is_deleted = 0
        `,
      [class_id]
    );

    // Also fetch the classroom name for better email context
    const [classroomData] = await mysqlServerConnection.query(
      `SELECT cls_name FROM ${req.user.db_name}.classroom WHERE id = ?`,
      [class_id]
    );

    const classroomName =
      classroomData.length > 0 ? classroomData[0].cls_name : "Classroom";

    if (userMails.length > 0) {
      // const classDetails = {
      //   className,
      //   description,
      //   classroomName,
      //   formattedDate: new Date(date).toLocaleDateString("en-US", {
      //     weekday: "long",
      //     year: "numeric",
      //     month: "long",
      //     day: "numeric",
      //   }),
      //   formattedTime: `${new Date(start_time).toLocaleTimeString("en-US", {
      //     hour: "2-digit",
      //     minute: "2-digit",
      //   })} - ${new Date(end_time).toLocaleTimeString("en-US", {
      //     hour: "2-digit",
      //     minute: "2-digit",
      //   })}`,
      // };
      // Send emails and notifications to all trainees
      // for (const mail of userMails) {
      //   // Send email notification
      //   // sendLiveClassEmail(
      //   //   mail.email,
      //   //   `New Live Class Scheduled for ${classroomName}`,
      //   //   `Live Class: ${className}`,
      //   //   classDetails,
      //   //   false
      //   // );
      //   // Add notification in the database
      //   // await mysqlServerConnection.query(
      //   //   `
      //   //   INSERT INTO ${req.user.db_name}.notifications
      //   //   (user_id, title, body, is_read, is_deleted) VALUES
      //   //   (?, ?, ?, ?, ?);
      //   //   `,
      //   //   [
      //   //     mail.id,
      //   //     "New Live Class Scheduled",
      //   //     `Live Class: ${className} has been scheduled for ${classroomName}, Date: ${date}, Time: ${start_time} - ${end_time}`,
      //   //     false,
      //   //     false,
      //   //   ]
      //   // );
      // }
    }

    console.log("INSERT LIVE CLASS STATUS", result);

    // Send success response
    res.status(201).json({
      success: true,
      statusCode: 201,
      liveClassId: result.insertId,
      message: "Live class created successfully!",
    });
  } catch (error) {
    console.error("Error creating live class:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const getLiveClasses = async (req, res, next) => {
  try {
    // Get parameters from request body
    const {
      class_id,
      page = 1,
      limit = 10,
      search = "",
      startDate = null,
      endDate = null,
    } = req.body;

    const dbName = req.user.db_name;
    const offset = (page - 1) * limit;

    console.log("GET LIVE CLASSES", req.body);

    // Validate required fields
    if (!class_id) {
      return res.status(400).json({
        success: false,
        message: "Class ID is required",
      });
    }

    // Build the base query
    let query = `
      SELECT lc.id, lc.class_name, lc.class_desc, lc.class_date, lc.start_time, lc.end_time, 
      lc.class_id, lc.live_class_url, lc.created_at, lc.updated_at, lc.is_active, lc.is_deleted,
      (SELECT COUNT(DISTINCT user_id) FROM ${dbName}.live_class_logs WHERE live_class_id = lc.id) AS attendance_count
      FROM ${dbName}.live_class AS lc
      WHERE lc.is_deleted = false AND lc.class_id = ?`;

    // Initialize params array with class_id
    let params = [class_id];

    // Add search condition if provided
    if (search) {
      query += ` AND (lc.class_name LIKE ? OR lc.class_desc LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    // Add date range conditions if provided
    if (startDate) {
      query += ` AND lc.class_date >= ?`;
      params.push(startDate);
    }

    if (endDate) {
      query += ` AND lc.class_date <= ?`;
      params.push(endDate);
    }

    // Get total count for pagination
    const countQuery = `SELECT COUNT(*) as total FROM (${query}) as filtered`;
    const [countResult] = await mysqlServerConnection.query(countQuery, params);
    const totalRecords = countResult[0].total;
    const totalPages = Math.ceil(totalRecords / limit);

    // Add sorting and pagination to the main query
    query += ` ORDER BY lc.start_time DESC LIMIT ? OFFSET ?`;
    params.push(Number(limit), offset);

    // Execute the query
    const [results] = await mysqlServerConnection.query(query, params);

    // Get the total count of attendees for all live classes in this classroom
    const [totalAttendanceResult] = await mysqlServerConnection.query(
      `
    SELECT COUNT(DISTINCT user_id) AS total_attendees
    FROM ${dbName}.live_class_logs lcl
    JOIN ${dbName}.live_class lc ON lcl.live_class_id = lc.id
    WHERE lc.class_id = ? AND lc.is_deleted = false
    `,
      [class_id]
    );

    const totalAttendees = totalAttendanceResult[0]?.total_attendees || 0;
    res.status(200).json({
      success: true,
      message: "Live classes retrieved successfully!",
      data: {
        liveClasses: results,
        totalAttendees,
        pagination: {
          totalRecords,
          totalPages,
          currentPage: Number(page),
          limit: Number(limit),
        },
      },
    });
  } catch (error) {
    console.error("Error retrieving live classes:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deleteLiveClass = async (req, res, next) => {
  try {
    const { live_class_id } = req.params;
    console.log("DELETE LIVE CLASS", req.params);
    if (!live_class_id) {
      return next({ statusCode: 404, message: "Please provide live class id" });
    }

    // Select live class including start_time and end_time
    const [data] = await mysqlServerConnection.query(
      `
            SELECT * 
            FROM ${req.user.db_name}.live_class 
            WHERE id = ? AND is_deleted = 0
        `,
      [live_class_id]
    );

    if (data.length === 0) {
      return next({ statusCode: 404, message: "Live class not found" });
    }

    const liveClass = data[0];
    const currentTime = new Date();

    // Check if the current time is between start_time and end_time
    const startTime = new Date(liveClass.start_time);
    const endTime = new Date(liveClass.end_time);

    if (liveClass.is_active) {
      return next({
        statusCode: 403,
        message: "Cannot delete live class while it is ongoing",
      });
    }

    // Proceed to mark the live class as deleted
    const [result] = await mysqlServerConnection.query(
      `
            UPDATE ${req.user.db_name}.live_class 
            SET is_deleted = 1 
            WHERE id = ?
        `,
      [live_class_id]
    );

    if (result.affectedRows === 0) {
      return next({
        statusCode: 404,
        message: "Live class not found or is already deleted",
      });
    }

    res.status(200).json({
      success: true,
      message: "Live class deleted successfully",
    });
  } catch (error) {
    console.log(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// Abid Modified
const updateLiveClass = async (req, res, next) => {
  try {
    const { live_class_id } = req.params;
    const {
      class_name,
      class_desc,
      class_date,
      start_time,
      end_time,
      class_id,
    } = req.body;
    console.log("UPDATE LIVE CLASS", req.body);

    // Validate if the live class exists
    const [existingClass] = await mysqlServerConnection.query(
      `SELECT * FROM ${req.user.db_name}.live_class WHERE id = ? AND is_deleted = false`,
      [live_class_id]
    );

    if (!existingClass || existingClass.length === 0) {
      return res.status(404).json({
        statusCode: 404,
        success: false,
        message: "Live class not found",
      });
    }

    // Validate date is not in the past
    const today = new Date();
    const selectedDate = new Date(class_date);
    selectedDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return res.status(400).json({
        statusCode: 400,
        success: false,
        message: "The selected date should not be less than today's date.",
      });
    }

    // Validate start time is not in the past for today's classes
    const selectedStartDateTime = new Date(start_time);
    const currentDateTime = new Date();

    if (
      selectedDate.getTime() === today.getTime() &&
      selectedStartDateTime < currentDateTime
    ) {
      return res.status(400).json({
        statusCode: 400,
        success: false,
        message: "The start time cannot be in the past for today's classes.",
      });
    }

    // Validate end time is after start time
    const selectedEndDateTime = new Date(end_time);
    if (selectedEndDateTime <= selectedStartDateTime) {
      return res.status(400).json({
        statusCode: 400,
        success: false,
        message: "The end time should be after the start time.",
      });
    }

    // Validate class duration is not more than 24 hours
    const durationHours =
      (selectedEndDateTime - selectedStartDateTime) / (1000 * 60 * 60);
    if (durationHours > 24) {
      return res.status(400).json({
        statusCode: 400,
        success: false,
        message: "Class duration cannot exceed 24 hours.",
      });
    }

    // Update the live class
    await mysqlServerConnection.query(
      `UPDATE ${req.user.db_name}.live_class 
             SET class_name = ?, class_desc = ?, class_date = ?, start_time = ?, end_time = ?, 
                 class_id = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
      [
        class_name,
        class_desc || null,
        class_date,
        start_time,
        end_time,
        class_id,
        live_class_id,
      ]
    );

    console.log("Live class updated successfully!");

    // Send success response
    res.status(200).json({
      statusCode: 200,
      success: true,
      message: "Live class updated successfully!",
    });
  } catch (error) {
    console.error("Error updating live class:", error.message);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

// Function to generate JWT
const getAccessToken = async () => {
  try {
    const response = await axios.post("https://zoom.us/oauth/token", null, {
      params: {
        grant_type: 2,
        code: 0,
        redirect_uri: redirectUri,
      },
      auth: {
        username: clientId,
        password: clientSecret,
      },
    });

    console.log("yes");
    return response.data.access_token;
  } catch (error) {
    throw null;
    // console.error('Error obtaining access token:', error);
    // res.status(500).json({ success: false, message: 'Failed to obtain access token' });
  }
};

// Endpoint to create a Zoom meeting
const CreateZoomLive = async (req, res) => {
  const { topic, startTime, duration } = req.body; // Get the meeting details and access token from the request

  try {
    const accessToken = await getAccessToken();

    // console.log(accessToken,'---------------------------------token')

    const response = await axios.post(
      "https://api.zoom.us/v2/users/me/meetings",
      {
        topic,
        type: 2, // 2 for scheduled meetings
        start_time: startTime, // Format: YYYY-MM-DDTHH:mm:ssZ
        duration,
        settings: {
          host_video: true,
          participant_video: true,
          mute_upon_entry: true,
          waiting_room: false,
          approval_type: 0, // Automatically approve
        },
      },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`, // Use the OAuth access token here
          "Content-Type": "application/json",
        },
      }
    );

    res.status(201).json({ success: true, data: response.data });
  } catch (error) {
    console.error("Error creating meeting:", error);
    res
      .status(500)
      .json({ success: false, message: "Failed to create meeting" });
  }
};

const resendLiveClassMail = async (req, res, next) => {
  try {
    const { live_class_id, classroomId } = req.body;
    console.log("RESEND LIVE CLASS MAIL", req.body);

    const [data] = await mysqlServerConnection.query(
      `
        SELECT * from ${req.user.db_name}.live_class where id = ? AND is_deleted = false
        `,
      [live_class_id]
    );

    if (data.length == 0) {
      return next({
        statusCode: 404,
        message: "Live class not found or is already deleted",
      });
    }

    console.log("DATA", data);
    const liveClass = data[0];

    // Fetch all trainees from the classroom_trainee table
    const [userMails] = await mysqlServerConnection.query(
      `
      SELECT u.email, u.id 
      FROM ${req.user.db_name}.classroom_trainee ct
      JOIN ${req.user.db_name}.users u ON ct.user_id = u.id
      WHERE ct.class_id = ? AND u.is_blocked = 0 AND u.is_deleted = 0
      `,
      [classroomId]
    );

    // Also fetch the classroom name for better email context
    const [classroomData] = await mysqlServerConnection.query(
      `SELECT cls_name FROM ${req.user.db_name}.classroom WHERE id = ?`,
      [classroomId]
    );

    const classroomName =
      classroomData.length > 0 ? classroomData[0].cls_name : "Classroom";

    console.log("USER MAILS", userMails);
    if (userMails.length > 0) {
      const classDetails = {
        className: liveClass.class_name,
        description: liveClass.class_desc,
        classroomName,
        formattedDate: new Date(liveClass.class_date).toLocaleDateString(
          "en-US",
          {
            weekday: "long",
            year: "numeric",
            month: "long",
            day: "numeric",
          }
        ),
        formattedTime: `${new Date(liveClass.start_time).toLocaleTimeString(
          "en-US",
          {
            hour: "2-digit",
            minute: "2-digit",
          }
        )} - ${new Date(liveClass.end_time).toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
        })}`,
      };

      // Send emails to all trainees
      for (const mail of userMails) {
        sendLiveClassEmail(
          mail.email,
          `Live Class Reminder for ${classroomName}`,
          `Live Class Reminder: ${liveClass.class_name}`,
          classDetails,
          true
        );

        // Add notification in the database
        await mysqlServerConnection.query(
          `
          INSERT INTO ${req.user.db_name}.notifications
          (user_id, title, body, is_read, is_deleted) VALUES
          (?, ?, ?, ?, ?);
          `,
          [
            mail.id,
            "Live Class Reminder",
            `Live Class: ${liveClass.class_name} reminder for ${classroomName}, Date: ${liveClass.class_date}, Time: ${liveClass.start_time} - ${liveClass.end_time}`,
            false,
            false,
          ]
        );
      }
    }

    res.status(200).json({
      success: true,
      message: "Reminder emails sent to all classroom trainees successfully",
    });
  } catch (error) {
    console.log(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const activateLiveClass = async (req, res, next) => {
  try {
    const { live_class_id, class_name, unique_code } = req.body;
    console.log("ACTIVATE LIVE CLASS", req.body);
    const dbName = req.user.db_name;

    // Generate the live class URL
    const liveClassUrl = `/${class_name}/${unique_code}`;

    // Update the live class to active status and set the URL
    await mysqlServerConnection.query(
      `
      UPDATE ${dbName}.live_class 
      SET is_active = 1, live_class_url = ?
      WHERE id = ?
      `,
      [liveClassUrl, live_class_id]
    );

    // Get the class_id from the live_class table
    const [liveClassData] = await mysqlServerConnection.query(
      `
      SELECT class_id, class_name FROM ${dbName}.live_class 
      WHERE id = ?
      `,
      [live_class_id]
    );

    if (liveClassData.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Live class not found",
      });
    }

    const classId = liveClassData[0].class_id;
    const liveClassName = liveClassData[0].class_name;

    // Get the classroom name
    const [classroomData] = await mysqlServerConnection.query(
      `
      SELECT cls_name FROM ${dbName}.classroom 
      WHERE id = ?
      `,
      [classId]
    );

    const classroomName =
      classroomData.length > 0 ? classroomData[0].cls_name : "Classroom";

    // Generate join URL for the response
    const baseUrl = process.env.PROD_FRONTEND_URL
      ? process.env.PROD_FRONTEND_URL
      : process.env.DEV_FRONTEND_URL;
    const joinUrl = `${baseUrl}/classroom/live-class${liveClassUrl}`;

    res.status(200).json({
      success: true,
      message: "Live class activated successfully",
      data: {
        class_id: classId,
        join_url: joinUrl,
        live_class_name: liveClassName,
        classroom_name: classroomName,
      },
    });

    console.log("ACTIVATED LIVE CLASS SUCCESSFULLY");
  } catch (error) {
    console.log(error);
    return next({
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const deactivateLiveClass = async (req, res, next) => {
  try {
    const { live_class_id, class_id, user_id } = req.body;
    console.log("DEACTIVATE LIVE CLASS", req.body);
    const dbName = req.user.db_name;

    // Check if the live class exists
    const [liveClassData] = await mysqlServerConnection.query(
      `
      SELECT id, class_id, class_name FROM ${dbName}.live_class 
      WHERE id = ? AND is_deleted = 0
      `,
      [live_class_id]
    );

    if (liveClassData.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Live class not found",
      });
    }

    // Update the live class to deactivate it
    await mysqlServerConnection.query(
      `
      UPDATE ${dbName}.live_class 
      SET is_active = 0, live_class_url = NULL
      WHERE id = ?
      `,
      [live_class_id]
    );

    return res.status(200).json({
      success: true,
      message: "Live class deactivated successfully",
      data: {
        live_class_id,
        class_id: liveClassData[0].class_id,
        class_name: liveClassData[0].class_name,
      },
    });
  } catch (error) {
    console.error("Error deactivating live class:", error);
    return res.status(500).json({
      success: false,
      statusCode: 500,
      message: error.message || "Internal Server Error",
    });
  }
};

const logUserJoinLiveClass = async (req, res) => {
  try {
    const { live_class_id, class_id, user_id } = req.body;
    const dbName = req.user.db_name;

    // Check if an entry already exists for this user and live class
    const [existingLog] = await mysqlServerConnection.query(
      `
          SELECT id, logout_time FROM ${dbName}.live_class_logs 
          WHERE live_class_id = ? AND user_id = ? AND class_id = ?
          ORDER BY id DESC LIMIT 1
          `,
      [live_class_id, user_id, class_id]
    );

    const currentTime = new Date();

    if (existingLog.length > 0) {
      // User has joined this live class before
      // Update the login_time and set logout_time to null
      await mysqlServerConnection.query(
        `
              UPDATE ${dbName}.live_class_logs 
              SET login_time = ?, logout_time = NULL, updatedAt = CURRENT_TIMESTAMP
              WHERE id = ?
              `,
        [currentTime, existingLog[0].id]
      );

      return res.status(200).json({
        success: true,
        message: "User rejoined live class, log updated",
        data: {
          log_id: existingLog[0].id,
          live_class_id,
          class_id,
          user_id,
          login_time: currentTime,
        },
      });
    } else {
      // First time user is joining this live class
      // Create a new entry
      const [result] = await mysqlServerConnection.query(
        `
              INSERT INTO ${dbName}.live_class_logs
              (class_id, user_id, live_class_id, login_time)
              VALUES (?, ?, ?, ?)
              `,
        [class_id, user_id, live_class_id, currentTime]
      );

      return res.status(201).json({
        success: true,
        message: "User joined live class, log created",
        data: {
          log_id: result.insertId,
          live_class_id,
          class_id,
          user_id,
          login_time: currentTime,
        },
      });
    }
  } catch (error) {
    console.error("Error logging user join live class:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

const logUserLeaveLiveClass = async (req, res) => {
  try {
    const { live_class_id, user_id } = req.body;
    const dbName = req.user.db_name;

    // Find the active session for this user and live class
    const [activeSession] = await mysqlServerConnection.query(
      `
          SELECT id FROM ${dbName}.live_class_logs 
          WHERE live_class_id = ? AND user_id = ? AND logout_time IS NULL
          ORDER BY id DESC LIMIT 1
          `,
      [live_class_id, user_id]
    );

    if (activeSession.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No active session found for this user in this live class",
      });
    }

    const currentTime = new Date();

    // Update the logout_time
    await mysqlServerConnection.query(
      `
          UPDATE ${dbName}.live_class_logs 
          SET logout_time = ?, updatedAt = CURRENT_TIMESTAMP
          WHERE id = ?
          `,
      [currentTime, activeSession[0].id]
    );

    return res.status(200).json({
      success: true,
      message: "User left live class, log updated",
      data: {
        log_id: activeSession[0].id,
        live_class_id,
        user_id,
        logout_time: currentTime,
      },
    });
  } catch (error) {
    console.error("Error logging user leave live class:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

/**
 * Log all users leaving a live class when admin ends it
 * Update the logout_time for all active sessions in the live class
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} - Response with success/error message
 */
const logEndLiveClass = async (req, res) => {
  try {
    const { live_class_id } = req.body;
    const dbName = req.user.db_name;

    const currentTime = new Date();

    // Update logout_time for all active sessions in this live class
    const [result] = await mysqlServerConnection.query(
      `
          UPDATE ${dbName}.live_class_logs 
          SET logout_time = ?, updatedAt = CURRENT_TIMESTAMP
          WHERE live_class_id = ? AND logout_time IS NULL
          `,
      [currentTime, live_class_id]
    );

    return res.status(200).json({
      success: true,
      message: "Live class ended, all user logs updated",
      data: {
        live_class_id,
        users_updated: result.affectedRows,
        logout_time: currentTime,
      },
    });
  } catch (error) {
    console.error("Error logging end live class:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

// Abid Modified
const getAllTrainers = async (req, res) => {
  // console.log("getAllTrainers");
  try {
    const dbName = req.user.db_name;

    const [trainers] = await mysqlServerConnection.query(
      `
      SELECT u.id AS user_id, u.name, u.email
      FROM ${dbName}.users u
      JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
      JOIN ${dbName}.roles r ON ur.role_id = r.id
        WHERE r.name LIKE '%trainer%'
        AND u.is_blocked = 0
        AND u.is_deleted = 0
      `
    );

    // console.log(trainers);

    if (trainers.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No trainers found",
      });
    }

    return res.status(200).json({
      success: true,
      total_trainers: trainers.length,
      trainers: trainers.map((trainer) => ({
        user_id: trainer.user_id,
        name: trainer.name,
      })),
    });
  } catch (error) {
    console.error("Error fetching all trainers:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

const classroomDashboard = async (req, res, next) => {
  try {
    const { classroom_id } = req.body;
    const dbName = req.user.db_name;

    if (!classroom_id) {
      return res.status(400).json({
        success: false,
        message: "classroom_id is required",
      });
    }

    // Step 1: Fetch trainers
    const [trainers] = await mysqlServerConnection.query(
      `
      SELECT 
        u.id AS user_id, 
        u.name, 
        u.email, 
        u.mobile,
        u.mobileno_code,
        CONCAT(u.mobileno_code, ' ', u.mobile) AS phone,
        u.profile_pic_url,
        r.name AS role
      FROM ${dbName}.classroom_trainer ct
      JOIN ${dbName}.users u ON ct.user_id = u.id
      JOIN ${dbName}.user_roles ur ON ur.user_id = u.id
      JOIN ${dbName}.roles r ON ur.role_id = r.id
      WHERE ct.classroom_id = ?
        AND u.is_deleted = 0
        AND u.is_blocked = 0
        AND r.name NOT IN ('admin', 'super_admin')
      `,
      [classroom_id]
    );

    // Step 2: Fetch group room info
    const [groupRoomRows] = await mysqlServerConnection.query(
      `SELECT id AS group_id, group_name FROM ${dbName}.group_rooms WHERE class_id = ?`,
      [classroom_id]
    );

    const groupRoom = groupRoomRows.length > 0 ? groupRoomRows[0] : null;

    // Step 3: Fetch cls_name and is_visibility from classroom
    const [classroomRows] = await mysqlServerConnection.query(
      `SELECT cls_name, is_visibility FROM ${dbName}.classroom WHERE id = ? AND is_deleted = 0`,
      [classroom_id]
    );

    const classroomInfo = classroomRows.length > 0 ? classroomRows[0] : null;

    if (trainers.length === 0) {
      return res.status(200).json({
        success: false,
        message: "No trainers are available",
        group: groupRoom,
        cls_name: classroomInfo?.cls_name || null,
        is_visibility: classroomInfo?.is_visibility || null,
      });
    }

    res.status(200).json({
      success: true,
      message: "Trainer data fetched successfully",
      data: trainers,
      group: groupRoom,
      cls_name: classroomInfo?.cls_name || null,
      is_visibility: classroomInfo?.is_visibility || null,
    });
  } catch (error) {
    console.error("Error fetching classroom dashboard:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};




const changeTheVisibilityOfTabs = async (req, res, next) => {
  try {
    const { classroom_id, is_visibility } = req.body;
    console.log("changeTheVisibilityOfTabs", classroom_id, is_visibility);
    const dbName = req.user.db_name;

    if (!classroom_id || !Array.isArray(is_visibility)) {
      return res.status(400).json({
        success: false,
        message: "classroom_id and is_visibility (as array) are required",
      });
    }

    const visibilityJSON = JSON.stringify(is_visibility);

    const [result] = await mysqlServerConnection.query(
      `UPDATE ${dbName}.classroom 
       SET is_visibility = ? 
       WHERE id = ? AND is_deleted = 0`,
      [visibilityJSON, classroom_id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "Classroom not found or already deleted",
      });
    }

    return res.status(200).json({
      success: true,
      message: "is_visibility updated successfully",
      data: {
        classroom_id,
        is_visibility,
      },
    });
  } catch (error) {
    console.error("❌ Error in changeTheVisibilityOfTabs:", error);
    return res.status(500).json({
      success: false,
      message: error.message || "Internal Server Error",
    });
  }
};

module.exports = {
  getAllClasses,
  getAllStudentApproval,
  createClassroom,
  getAllTrainers,
  createLiveClass,
  deleteClassRoom, 
  approveOrDenyClassroom,
  getLiveClasses,
  CreateZoomLive,
  getAccessToken,
  deleteLiveClass,
  updateLiveClass,
  resendLiveClassMail,
  activateLiveClass,
  updateClassroom,
  activeDeactiveClassroom,
  deactivateLiveClass,
  logEndLiveClass,
  logUserJoinLiveClass,
  logUserLeaveLiveClass,
  classroomDashboard,
  changeTheVisibilityOfTabs,
};
