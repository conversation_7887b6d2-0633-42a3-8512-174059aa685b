# Zoom Meeting SDK Integration - Testing Guide

## Overview
This guide provides comprehensive testing instructions for the Zoom Meeting SDK integration in the LMS application.

## Prerequisites

### Environment Variables
Ensure the following environment variables are set in your backend `.env` file:

```env
# Zoom Server-to-Server OAuth
ZOOM_ACCOUNT_ID=your_zoom_account_id
ZOOM_CLIENT_ID=your_zoom_client_id
ZOOM_CLIENT_SECRET=your_zoom_client_secret

# Zoom Meeting SDK
Zoom_SDK_CLIENT_ID=your_zoom_sdk_client_id
ZOOM_SDK_CLIENT_SECRET=your_zoom_sdk_client_secret

# Frontend URL (for generating meeting URLs)
FRONTEND_URL=http://test.localhost:3001
```

### Database Migration
Run the database migration to add the `is_ended` column:

```sql
-- Execute this SQL on your database
ALTER TABLE classroom_live_class 
ADD COLUMN is_ended BOOLEAN DEFAULT FALSE AFTER is_started;
```

## Testing Flow

### 1. Admin Creates Live Class

**Endpoint:** `POST /mainOrg/classroom/zoom_live_class`

**Test Data:**
```json
{
  "classroom_id": 1,
  "live_class_name": "Test React Session",
  "live_class_description": "Testing Zoom integration",
  "time_zone": "Asia/Kolkata",
  "class_date": "2025-01-22",
  "start_time": "2025-01-22T10:00:00Z",
  "duration": "60 minutes"
}
```

**Expected Result:**
- Meeting created in Zoom
- Record inserted in `classroom_live_class` table
- Host and join URLs generated with SDK parameters
- Response includes meeting_id and URLs

### 2. Admin Views Live Classes

**Endpoint:** `POST /mainOrg/classroom/zoom_live_classes`

**Test Data:**
```json
{
  "classroom_id": 1
}
```

**Expected Result:**
- List of live classes for the classroom
- Each class shows status (scheduled/started/ended)
- Created by name is populated

### 3. Admin Starts Meeting

**Endpoint:** `POST /mainOrg/classroom/zoom_live_class/start`

**Test Data:**
```json
{
  "live_class_id": 1
}
```

**Expected Result:**
- `is_started` set to `true` in database
- `status` updated to 'started'
- Fresh host URL generated with new signature
- Admin can click to join as host

### 4. Trainee Generates Join Token

**Endpoint:** `POST /mainOrg/classroom/zoom_live_class/join_token`

**Test Data:**
```json
{
  "live_class_id": 1
}
```

**Expected Result:**
- Fresh join URL generated with attendee signature
- Meeting must be started (is_started = true)
- Trainee can click to join as attendee

### 5. Zoom Meeting SDK Integration

**Test Steps:**
1. Admin clicks "Start Meeting" - should open `/zoom-meeting?meetingNumber=...&role=1&...`
2. Trainee clicks "Join Meeting" - should open `/zoom-meeting?meetingNumber=...&role=0&...`
3. Zoom SDK should load and initialize
4. Users should be able to join the meeting directly

### 6. Admin Ends Meeting

**Endpoint:** `POST /mainOrg/classroom/zoom_live_class/end`

**Test Data:**
```json
{
  "live_class_id": 1
}
```

**Expected Result:**
- `status` updated to 'ended'
- `is_ended` set to `true`
- Zoom meeting ended via API
- No more join attempts allowed

### 7. Admin Deletes Live Class

**Endpoint:** `DELETE /mainOrg/classroom/zoom_live_class/{live_class_id}`

**Expected Result:**
- Cannot delete active meetings
- Zoom meeting deleted via API
- Database record soft deleted (`is_deleted` = true)

## Frontend Testing

### Admin Component (`ClassroomLiveClasses.jsx`)

**Test Cases:**
1. **Create Modal:** All form fields work, validation works
2. **Edit Modal:** Pre-populated fields, restricted editing for started meetings
3. **Delete Modal:** Confirmation dialog, proper error handling
4. **Meeting Actions:** Start/Join/End buttons work correctly
5. **Status Badges:** Correct status display (Scheduled/Live/Ended)

### Trainee Component (`LiveClass.jsx`)

**Test Cases:**
1. **View Live Classes:** Shows all classes for classroom
2. **Join Button:** Only enabled for started meetings
3. **Status Display:** Correct status for each meeting
4. **Summary Cards:** Shows live, upcoming, and completed counts

### Zoom Meeting Component (`ZoomMeeting.jsx`)

**Test Cases:**
1. **Parameter Validation:** Handles missing parameters gracefully
2. **SDK Loading:** Loads Zoom SDK scripts correctly
3. **Meeting Join:** Successfully joins meeting with provided signature
4. **Error Handling:** Shows appropriate error messages
5. **Cleanup:** Properly leaves meeting on component unmount

## API Testing with Postman/Curl

### Create Live Class
```bash
curl -X POST http://localhost:4000/mainOrg/classroom/zoom_live_class \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "classroom_id": 1,
    "live_class_name": "Test Session",
    "live_class_description": "Testing",
    "time_zone": "Asia/Kolkata",
    "class_date": "2025-01-22",
    "start_time": "2025-01-22T10:00:00Z",
    "duration": "60 minutes"
  }'
```

### Start Meeting
```bash
curl -X POST http://localhost:4000/mainOrg/classroom/zoom_live_class/start \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"live_class_id": 1}'
```

## Common Issues and Troubleshooting

### 1. Zoom SDK Not Loading
- Check browser console for script loading errors
- Verify internet connection
- Try different browser

### 2. Meeting Join Fails
- Verify signature generation is correct
- Check if meeting exists in Zoom
- Ensure meeting is started

### 3. Database Errors
- Run the migration script for `is_ended` column
- Check foreign key constraints
- Verify user permissions

### 4. Environment Variables
- Double-check all Zoom credentials
- Ensure FRONTEND_URL is correct
- Restart backend after env changes

## Success Criteria

✅ Admin can create live classes with Zoom meetings
✅ Admin can start meetings and join as host
✅ Trainees can join started meetings as attendees
✅ Admin can end meetings
✅ All database operations work correctly
✅ Zoom SDK integration works without browser redirects
✅ Proper error handling throughout the flow
✅ Meeting status updates correctly in real-time

## Security Considerations

- Signatures are generated server-side with proper timestamps
- Meeting URLs include secure tokens
- User authentication required for all operations
- Zoom credentials stored securely in environment variables
