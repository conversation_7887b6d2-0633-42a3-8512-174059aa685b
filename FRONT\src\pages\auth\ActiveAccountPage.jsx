import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import logo from '../../assets/images/logo/logo.png';
import { toast } from 'react-toastify';
import { validateOTP, validateForm } from '../../utils/validation';
import { verifyRegisterOTPApi, sendOTPApi } from '../../services/authService';
import { ActiveAccountOTPFromLoginPage } from '../../services/authService';

function ActiveAccountPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email;
  const domain = window.location.origin;

    console.log("OTP Page", email);
    console.log("OTP Page", domain);
    console.log("OTP Page", location);
    

  const [formData, setFormData] = useState({
    otp: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);

  // Validation Rules
  const validationRules = {
    otp: validateOTP
  };

  // Handle Change
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Only allow digits and limit to 6 characters
    if (name === 'otp' && !/^\d*$/.test(value)) {
      return;
    }

    // Update form data and clear errors
    const newFormData = { ...formData, [name]: value.slice(0, 6) };
    const newErrors = { ...errors, [name]: undefined };

    setFormData(newFormData);
    setErrors(newErrors);
  };

  // Handle Resend OTP
  const handleResendOTP = async () => {
    if (!email) {
      toast.error('Email not found. Please go back to login.', {
        position: "top-center",
        autoClose: 3000
      });
      return;
    }

    setIsResending(true);
    try {
      const payload = {
        email: email,
        domain: domain
      };

      await ActiveAccountOTPFromLoginPage(payload);
      toast.success('OTP sent successfully!', {
        position: "top-center",
        autoClose: 2000
      });
    } catch (error) {
      console.error('Resend OTP Error:', error);
      toast.error(error.response?.data?.message || 'Failed to resend OTP. Please try again.', {
        position: "top-center",
        autoClose: 3000
      });
    } finally {
      setIsResending(false);
    }
  };

  // Handle Submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    const validation = validateForm(formData, validationRules);
    if (validation.isValid) {
      setIsLoading(true);

      try {
        const payload = {
          email: email,
          domain: domain,
          otp: formData.otp
        };

        const response = await verifyRegisterOTPApi(payload);
        console.log('OTP Verification Response:', response);

        if (response.success) {
          toast.success('Account activated successfully!', {
            position: "top-center",
            autoClose: 2000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            onClose: () => {
    navigate('/auth/login');
  }
          });
        } else {
          throw new Error(response.message || 'Invalid OTP');
        }
      } catch (error) {
        console.error('OTP Verification Error:', error);
        toast.error(error.response?.data?.message || 'Failed to verify OTP. Please try again.', {
          position: "top-center",
          autoClose: 2000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true
        });
        setIsLoading(false);
      }
    } else {
      setErrors(validation.errors);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-logo">
        <img src={logo} alt="LMS Logo" style={{ width: '180px', height: 'auto' }} />
            </div>

      <div className="auth-card">
        <div className="auth-header">
          <h2>Activate Your Account</h2>
          <p>Enter the 6-digit code sent to {email ? email : 'your email'}</p>
                </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-group">
            <label htmlFor="otp">Enter OTP</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:key-outline" className="auth-input-icon" />
              <input
                type="text"
                id="otp"
                name="otp"
                value={formData.otp}
                onChange={handleChange}
                placeholder="Enter 6-digit OTP"
                className={`auth-input ${errors.otp ? 'error' : ''}`}
                disabled={isLoading}
                maxLength={6}
              />
            </div>
            {errors.otp && <span className="auth-error-message">{errors.otp[0]}</span>}
          </div>

          <button
            type="submit"
            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="spinner"></div>
                Activating Account...
              </>
            ) : (
              'Activate Account'
            )}
          </button>

          <div className="auth-options">
            <div style={{ marginBottom: '10px' }}>
              Didn't receive the code?{' '}
              <button
                type="button"
                onClick={handleResendOTP}
                disabled={isLoading || isResending}
                style={{
                  background: 'none',
                  border: 'none',
                  color: '#3152e8',
                  cursor: isLoading || isResending ? 'not-allowed' : 'pointer',
                  opacity: isLoading || isResending ? 0.6 : 1
                }}
              >
                {isResending ? 'Sending...' : 'Resend OTP'}
              </button>
            </div>
            <span>Back to </span>
            <Link to="/auth/login" className={isLoading ? 'disabled-link' : ''}>Login</Link>
        </div>
        </form>
      </div>
    </div>
  );
}

export default ActiveAccountPage;