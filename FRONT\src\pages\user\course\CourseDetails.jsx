import React, { useState, useEffect } from 'react';
import {
  FaVideo, FaDownload, FaInfinity, FaCertificate, FaShareAlt,
  FaTwitter, FaLinkedin, FaChevronDown, FaChevronUp, FaLock
} from 'react-icons/fa';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Icon } from '@iconify/react';
import courseImg from '../../../assets/images/course/course1.png';
import './CourseDetails.css';
import DefaultProfile from '../../../assets/images/profile/default-profile.png';
import { decodeData, encodeData } from '../../../utils/encodeAndEncode';
import { getMetadataCourseDetails, AddMyCourses } from '../../../services/userService';
import NoData from '../../../components/common/NoData';

function CourseDetails() {


  const navigate = useNavigate();
  const [expandedModules, setExpandedModules] = useState(['module1']);
  const [isEnrolling, setIsEnrolling] = useState(false);
  const [isPaidEnrolling, setIsPaidEnrolling] = useState(false);


  const { encodedId } = useParams();
  const decoded = decodeData(encodedId);
  const courseId = decoded?.id;

  useEffect(() => {
    console.log('Decoded Course ID:', courseId);
  }, [courseId]);


  const [courseData, setCourseData] = useState(null);

  useEffect(() => {
    getCourseDetails();
  }, [courseId]);

  async function getCourseDetails() {
    try {
      console.log('Fetching course detail......');
      const response = await getMetadataCourseDetails({
        course_id: courseId
      });
      console.log('Course detail fetched successfully-------------:', response.data);
      setCourseData(response.data);
    } catch (error) {
      console.error('Error fetching course details:', error);
    }
  }

  async function EnrollFreeCourse() {
    try {
      setIsEnrolling(true);

      const response = await AddMyCourses({
        course_id: courseId
      });

      // Simulate 2-second loader
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Enrolled successfully:', response);

      // Show toast after loading
      toast.success('Enrolled successfully! Redirecting...', {
        position: 'top-right',
        autoClose: 1000,
      });

      // Wait 1 second after toast before redirecting
      await new Promise(resolve => setTimeout(resolve, 1000));

      const encodedCourseId = encodeData({ id: courseId });
      navigate(`/user/courses/WatchCourse/${encodeURIComponent(encodedCourseId)}`);

    } catch (error) {
      console.error('Error enrolling in course:', error);
      toast.error('Failed to enroll in course. Please try again.');
    } finally {
      setIsEnrolling(false);
    }
  }

  const toggleModule = (moduleId) => {
    setExpandedModules(prev =>
      prev.includes(moduleId)
        ? prev.filter(id => id !== moduleId)
        : [...prev, moduleId]
    );
  };

  // Course Data 
  const courseModules = [
    {
      id: 'module1',
      title: 'Program Information 2023/2024 Edition',
      lectures: [
        { title: 'About The Course', duration: '01:20', preview: true },
        { title: 'Tools Introduction', duration: '07:50', preview: true },
        { title: 'Basic Document Structure', duration: '06:30', preview: true, locked: true },
        { title: 'HTML5 Foundations Certification Final Project', duration: '02:40', locked: true }
      ],
      lectureCount: '3 lectures',
      duration: '9 min'
    },
    {
      id: 'module2',
      title: 'Certified HTML5 Foundations 2023/2024',
      lectureCount: '3 lectures',
      duration: '9 min'
    },
    {
      id: 'module3',
      title: 'Your Development Toolbox',
      lectureCount: '3 lectures',
      duration: '9 min'
    },
    {
      id: 'module4',
      title: 'JavaScript Specialist',
      lectureCount: '3 lectures',
      duration: '9 min'
    }
  ];

  const handleWatchCourse = () => {
    navigate('/user/courses/courseDetails/WatchCourse');
  }

  const handlePaidEnrollment = async () => {
    setIsPaidEnrolling(true);

    // Simulate 2-second loading
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Log the data being passed
    console.log('Passing to OrderDetails:', {
      courseId,
      courseData
    });

    navigate('/user/courses/orderDetails', {
      state: {
        course_id: Number(courseId), // Convert to number and ensure it's passed correctly
        course_name: courseData?.course_name || '',
        course_price: courseData?.course_price || '0',
        banner_image: courseData?.banner_image || '',
        course_type: courseData?.course_type || 'Paid',
        course_desc: courseData?.course_desc || '',
        discountCode: null,
        discountValue: 0,
        points: 0
      }
    });

    setIsPaidEnrolling(false);
  };

  if (!courseData) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '60vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  const hasSubCategories = courseData.course_subcategory &&
    (() => {
      try {
        const parsed = JSON.parse(courseData.course_subcategory);
        return Array.isArray(parsed) && parsed.length > 0;
      } catch (e) {
        console.error('Error parsing subcategories:', e);
        return false;
      }
    })();

  const hasModules = courseData.modules &&
    Object.keys(courseData.modules).length > 0;

  return (
    <>
      <div className="row">
        <div className="col-12 p-0">
          <div className="course-details-header">
            <div className="container">
              <h3 className="fw-bold mb-3">{courseData.course_name || 'Untitled Course'}</h3>
              <p className="font-size-8">
                {courseData.tags && Array.isArray(courseData.tags) ? (
                  courseData.tags.join(' | ')
                ) : (
                  'No tags available'
                )}
              </p>
              <div className="d-flex flex-wrap align-items-center gap-4 mb-3">
                <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:star" className="text-warning" />
                  <span className="ms-2">{courseData?.courseMeta?.averageRating || '0.0'} rating</span>
                </div>
                <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:menu-book" className="me-2" />
                  <span>{courseData?.courseMeta?.modulesCount || 0} Modules</span>
                </div>
                {/* <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:group" className="me-2" />
                  <span>{courseData?.courseMeta?.enrolledCount || 0} Students</span>
                </div> */}
                <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:update" className="me-2" />
                  <span>Duration: {courseData?.courseMeta?.totalVideoDuration || '00:00:00'}</span>
                </div>
                <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:school" className="me-2" />
                  <span>Level: {courseData?.levels || 'Not specified'}</span>
                </div>
                <div className="d-flex align-items-center">
                  <Icon icon="material-symbols:language" className="me-2" />
                  <span>Language: {courseData?.course_language || 'Not specified'}</span>
                </div>
              </div>
              <div className="instructor d-flex align-items-center gap-2">
                <div className="instructor-image" style={{ width: '40px', height: '40px', minWidth: '40px' }}>
                  <img
                    src={courseData?.trainer?.profile || DefaultProfile}
                    alt={courseData?.trainer?.name}
                    className="rounded-circle"
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                </div>
                <div className='d-flex flex-column'>
                  <span className='font-size-1'>By {courseData?.trainer?.name || 'Loading...'}</span>
                  <span className='font-size-1'>{courseData?.trainer?.role || 'Instructor'}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="row justify-content-center">
            <div className="col-12 col-md-11">
              <div className="course-details-container">
                <div className="row">
                  <div className="col-lg-8">
                    <div className="what-youll-learn mb-4">
                      <h3 className="h4 mb-3">Course Info</h3>
                      <div className="learning-points">
                        {courseData.course_info && Array.isArray(courseData.course_info) ? (
                          courseData.course_info.map((point, index) => (
                            <div key={index} className="learning-point mb-3 d-flex align-items-center">
                              <Icon icon="material-symbols:check-circle" className="text-success me-3" />
                              <span>{point}</span>
                            </div>
                          ))
                        ) : (
                          <div className="learning-point mb-3 d-flex align-items-center">
                            <Icon icon="material-symbols:check-circle" className="text-success me-3" />
                            <span>Course information not available</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {courseData.course_desc ? (
                      <div className="about-course mb-4">
                        <h3 className="h4 mb-3">About This Course</h3>
                        <div className="about-content">
                          <p
                            className="text-muted mb-3"
                            style={{
                              wordBreak: 'break-word',
                              whiteSpace: 'normal',
                            }}
                          >
                            {courseData.course_desc}
                          </p>
                        </div>
                      </div>
                    ) : null}


                    <div className="course-content mb-4 shadow-none">
                      <h3 className="h4 mb-3">Course Content</h3>
                      {hasModules ? (
                        <div className="modules-list">
                          {Object.entries(courseData.modules).map(([moduleName, contents]) => (
                            <div key={moduleName} className="module-item">
                              <div
                                className="module-header d-flex align-items-center justify-content-between p-3"
                                onClick={() => toggleModule(moduleName)}
                                style={{ cursor: 'pointer' }}
                              >
                                <div className="d-flex align-items-center">
                                  {expandedModules.includes(moduleName) ? <FaChevronDown /> : <FaChevronUp />}
                                  <span className="ms-2">{moduleName}</span>
                                </div>
                                <div className="module-meta text-muted">
                                  <small>{contents.length} items</small>
                                </div>
                              </div>
                              {expandedModules.includes(moduleName) && (
                                <div className="module-content">
                                  {contents.map((content, idx) => (
                                    <div key={idx} className="lecture-item d-flex align-items-center justify-content-between p-3">
                                      <div className="d-flex align-items-center">
                                        {content.type === 'Video' && (
                                          <Icon icon="material-symbols:play-circle-outline" className="me-2" />
                                        )}
                                        {content.type === 'Document' && (
                                          <Icon icon="material-symbols:description-outline" className="me-2" />
                                        )}
                                        {content.type === 'Assessment' && (
                                          <Icon icon="material-symbols:quiz-outline" className="me-2" />
                                        )}
                                        {content.type === 'Survey' && (
                                          <Icon icon="material-symbols:analytics-outline" className="me-2" />
                                        )}
                                        <span>{content.title}</span>
                                      </div>
                                      <div className="d-flex align-items-center">
                                        {content.type === 'Video' && (
                                          <span className="ms-3 text-muted">{content.duration}</span>
                                        )}
                                        {content.type === 'Document' && (
                                          <span className="ms-3 text-muted">{content.fileType}</span>
                                        )}
                                        {content.type === 'Assessment' && content.questions && (
                                          <span className="ms-3 text-muted">{content.questions}</span>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}

                            </div>
                          ))}
                        </div>
                      ) : (
                        <NoData caption="No course content available yet" />
                      )}
                    </div>

                    {courseData.trainer ? (
                      <div className="instructor-profile mb-4">
                        <h3 className="h4 mb-4">Instructor</h3>
                        <div className="d-flex flex-column flex-md-row gap-4">

                          {/* Image Wrapper */}
                          <div
                            className="instructor-image-lg d-none d-md-block"
                            style={{
                              width: '150px',
                              height: '150px',
                              minWidth: '150px',
                              overflow: 'hidden',
                              borderRadius: '12px',
                              position: 'relative',
                              backgroundColor: '#f8f9fa'
                            }}
                          >
                            <img
                              src={courseData?.trainer?.profile || DefaultProfile}
                              alt={courseData?.trainer?.name}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover'
                              }}
                            />
                          </div>

                          {/* Instructor Info */}
                          <div className="instructor-info">
                            <h4 className="h5 mb-1">{courseData?.trainer?.name || 'Loading...'}</h4>
                            <p className="text-muted mb-3">
                              {courseData?.trainer?.role || 'Fire Management Specialist & Environmental Trainer'}
                            </p>

                            {/* <div className="d-flex flex-wrap align-items-center gap-4 mb-3"> */}

                            {/* <div className="d-flex align-items-center">
                                <span className="me-2">4.5</span>
                                <div className="rating-stars">
                                  <Icon icon="material-symbols:star" className="text-warning" />
                                  <Icon icon="material-symbols:star" className="text-warning" />
                                  <Icon icon="material-symbols:star" className="text-warning" />
                                  <Icon icon="material-symbols:star" className="text-warning" />
                                  <Icon icon="material-symbols:star-half" className="text-warning" />
                                </div>
                                <span className="ms-2">6 Reviews</span>
                              </div> */}
                            {/* 
                              <div className="d-flex align-items-center">
                                <Icon icon="material-symbols:group" className="me-2" />
                                <span>0 Students</span>
                              </div>

                              <div className="d-flex align-items-center">
                                <Icon icon="material-symbols:play-circle" className="me-2" />
                                <span>0 Courses</span>
                              </div> */}
                            {/* </div> */}

                            <p className="text-muted mb-4">
                              {courseData?.trainer?.bio || 'No bio available'}
                            </p>
                          </div>
                        </div>
                      </div>


                    ) : (
                      <NoData caption="Instructor information not available" />
                    )}
                  </div>

                  <div className="col-lg-4">
                    <div className="course-card card shadow-sm" style={{ position: 'relative', top: '-5rem' }}>
                      <img
                        src={courseData.banner_image || courseImg}
                        className="img-fluid border-2 m-2"
                        alt={courseData.course_name || "Course Preview"}
                        onError={(e) => {
                          e.target.src = courseImg;
                          e.target.onerror = null;
                        }}
                      />
                      <div className="card-body">
                        <div className="d-flex justify-content-between align-items-center mb-3">
                          <span className="price-tag">$ {courseData?.course_type?.toLowerCase() === 'free' ? '0' : courseData?.course_price || '0'}</span>
                          {courseData?.course_type?.toLowerCase() === 'free' ? (
                            <span className="original-price">$0</span>
                          ) : (
                            <span className="original-price">${Number(courseData?.course_price || 0) + 5}</span>
                          )}
                        </div>

                        {courseData?.course_type?.toLowerCase() === 'paid' ? (
                          <button
                            onClick={handlePaidEnrollment}
                            className="paid-btn"
                            disabled={isPaidEnrolling}
                          >
                            {isPaidEnrolling ? (
                              <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Processing...
                              </>
                            ) : (
                              <>
                                <Icon
                                  icon="mdi:lock"
                                  className="btn-icon"
                                />
                                Enroll Now
                              </>
                            )}
                          </button>
                        ) : (
                          <button
                            className="watch-now-btn free-btn"
                            onClick={courseData.is_course_purchase ? () => {
                              const encodedCourseId = encodeData({ id: courseId });
                              navigate(`/user/courses/WatchCourse/${encodeURIComponent(encodedCourseId)}`);
                            } : EnrollFreeCourse}
                            disabled={isEnrolling}
                          >
                            {isEnrolling ? (
                              <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Enrolling...
                              </>
                            ) : (
                              <>
                                <Icon
                                  icon="mdi:play-circle"
                                  className="btn-icon"
                                />
                                {courseData.is_course_purchase ? 'Continue Learning' : 'Watch Now'}
                              </>
                            )}
                          </button>
                        )}


                        <div className="d-flex justify-content-center align-items-center">
                          <FaInfinity className="text-muted me-2" />
                          Full lifetime access
                        </div>

                        <div className="course-includes">
                          <h4 className="h6 mb-3">This course includes:</h4>
                          {hasModules ? (
                            <ul className="list-unstyled">
                              <li className="mb-2 d-flex align-items-center">
                                <Icon icon="material-symbols:menu-book" className="text-muted me-2" />
                                {courseData?.courseMeta?.modulesCount || 0} Modules
                              </li>
                              <li className="mb-2 d-flex align-items-center">
                                <FaVideo className="text-muted me-2" />
                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Video').length || 0} Videos • {courseData?.courseMeta?.totalVideoDuration || '00:00:00'} total duration
                              </li>
                              <li className="mb-2 d-flex align-items-center">
                                <Icon icon="material-symbols:quiz" className="text-muted me-2" />
                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Assessment').length || 0} Assessments
                              </li>
                              <li className="mb-2 d-flex align-items-center">
                                <Icon icon="material-symbols:description-outline" className="text-muted me-2" />
                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Document').length || 0} Documents
                              </li>
                              <li className="mb-2 d-flex align-items-center">
                                <Icon icon="material-symbols:analytics-outline" className="text-muted me-2" />
                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Survey').length || 0} Surveys
                              </li>
                              {/* <li className="mb-2 d-flex align-items-center">
                                <Icon icon="material-symbols:group" className="text-muted me-2" />
                                {courseData?.courseMeta?.enrolledCount || 0} Total Enrollments
                              </li> */}
                              <li className="mb-2 d-flex align-items-center">
                                <FaInfinity className="text-muted me-2" />
                                Full lifetime access
                              </li>
                              <li className="mb-2 d-flex align-items-center">
                                <FaCertificate className="text-muted me-2" />
                                Certificate of completion
                              </li>
                            </ul>
                          ) : (
                            <p className="text-muted">Course content is being prepared</p>
                          )}
                        </div>



                      </div>
                    </div>
                  </div>


                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </>
  );
}

export default CourseDetails;