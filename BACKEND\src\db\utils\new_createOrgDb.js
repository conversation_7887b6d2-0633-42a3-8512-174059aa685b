
const { mysqlServerConnection } = require("../db");
const maindb = process.env.MAIN_DB;
async function createOrganizationDatabase(dbName) {
    const connection = await mysqlServerConnection.getConnection();

    try {
        // Start transaction
        await connection.query("START TRANSACTION");

        await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
        console.log(`Database ${dbName} created successfully.`);

        // -------------- Updated Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            profile_pic_url VARCHAR(255),
            benner_img_url VARCHAR(255),
            profession VARCHAR(50),
            qualification VARCHAR(40),
            experience VARCHAR(25),
            skill VARCHAR(225),
            name <PERSON><PERSON><PERSON><PERSON>(100),
            db_name VA<PERSON>HA<PERSON>(50) NOT NULL,
            password VARCHAR(255),
            email VARCHAR(320) NOT NULL UNIQUE,
            mobileno_code VARCHAR(45),
            mobile VARCHAR(25),
            otp_code VARCHAR(10),
            otp_expiry DATETIME,
            auth_provider VARCHAR(50) NOT NULL DEFAULT 'manual',
            bio VARCHAR(500),
            country VARCHAR(50),
            state VARCHAR(255),
            zipcode VARCHAR(10),
            language VARCHAR(50),
            address VARCHAR(1000),
            fcm_token VARCHAR(255),
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_email_verified TINYINT(1) DEFAULT 0,
            is_phone_verified TINYINT(1) DEFAULT 0,
            is_blocked TINYINT(1) DEFAULT 0,
            is_verified TINYINT(1) DEFAULT 0,
            is_deleted TINYINT(1) DEFAULT 0,
            employee_no VARCHAR(50),
            company_code VARCHAR(50),
            company_description VARCHAR(255),
            ic_no VARBINARY(255),
            fin_no VARBINARY(255),
            gender VARCHAR(10),
            date_of_birth DATE,
            date_joined DATE,
            pwm_rank VARCHAR(1000),
            job_title VARCHAR(1000)
        );
    `);

        // -------------- Updated Task Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.task_list (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        task TEXT NOT NULL,
        marked TINYINT(1) DEFAULT 0,        
        is_deleted TINYINT(1) DEFAULT 0,    
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
        );`);


        // -------------- Updated Task Lists Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.task_lists (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            classroom_id INT DEFAULT NULL,
            assessment_id INT DEFAULT NULL,
            assignment_id INT DEFAULT NULL,
            is_seen TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
        );
    `);

        // -------------- Updated Ticket Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.ticket (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            FOREIGN KEY (sender_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
            assigned_to_id INT  NULL,
            FOREIGN KEY (assigned_to_id) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
            subject text,
            description text,
            file_url VARCHAR(225) NULL,
            status VARCHAR(225) NOT NULL, 
            ticket_id VARCHAR(65) NOT NULL, 
            is_deleted BOOLEAN DEFAULT FALSE,
            response TEXT DEFAULT NULL,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `);

        // -------------- Updated Token Blacklist Tables --------------
        await connection.query(`CREATE TABLE IF NOT EXISTS ${dbName}.token_blacklist (
        id INT AUTO_INCREMENT PRIMARY KEY,
        token TEXT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );`);


        // -------------- Updated User Logs Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.user_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            log_name VARCHAR(225) NOT NULL,
            log_description TEXT NULL,
            file_url VARCHAR(225) NULL, 
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
        );
    `);

        // -------------- Updated User Points Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.user_points (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
        total_earn VARCHAR(255) NULL DEFAULT '0',
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `);


        // -------------- Updated User Rating Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.user_rating (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            mentor_id INT NOT NULL,
            course_id INT NOT NULL,
    
            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
            FOREIGN KEY (mentor_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
            FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
    
            class_rating INT NOT NULL,
            mentor_rating INT NOT NULL,
            feedback_text TEXT NULL,
    
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `);


        // -------------- Updated User Roles Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.user_roles (
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        PRIMARY KEY (user_id, role_id),
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `);

        // -------------- Updated Video Details Tables --------------
        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.video_details (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        FOREIGN KEY (video_id) REFERENCES ${dbName}.videos(id) ON DELETE CASCADE,
        attachment_type VARCHAR(50) NOT NULL,
        attachment_detail VARCHAR(255) NOT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `);

        // -------------- Updated Video Question Answer Tables --------------
        await connection.query(`CREATE TABLE IF NOT EXISTS ${dbName}.video_question_answer (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
        answer VARCHAR(500) NOT NULL,
        created_by INT NULL,
        FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_deleted boolean default false
        );`);


        // -------------- Updated Videos Tables --------------
        await connection.query(`CREATE TABLE IF NOT EXISTS ${dbName}.videos (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_id INT NOT NULL,
            FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
            module_id INT NULL,
            FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE SET NULL,
            video_name TEXT NOT NULL,
            upload_view_type VARCHAR(50) NOT NULL DEFAULT 'uploaded',
            video_url VARCHAR(255) NOT NULL,
            video_title LONGTEXT NULL,
            video_description LONGTEXT NULL,
            video_duration VARCHAR(25) DEFAULT NULL,
            serial_no VARCHAR(45) DEFAULT '0',
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_deleted boolean default false,
            is_active BOOLEAN DEFAULT TRUE
            );`);


        // -------------- Updated Videos Questions Tables --------------
        await connection.query(`CREATE TABLE IF NOT EXISTS ${dbName}.videos_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            video_id INT NOT NULL,
            FOREIGN KEY (video_id) REFERENCES ${dbName}.videos(id) ON DELETE CASCADE,
            question VARCHAR(128) NOT NULL,
            question_type VARCHAR(50) NOT NULL,
            execute_time VARCHAR(50) NOT NULL,
            options TEXT NULL,
            correct_option VARCHAR(250) DEFAULT '[]',
            created_by INT NULL,
            FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_deleted boolean default false
            );`);



        // -------------- Updated Announcements Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.announcements (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    createdBy INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    time_zone VARCHAR(100),
                    scheduleDateTime DATETIME,
                    status BOOLEAN DEFAULT TRUE,
                    roles INT NULL,
                    is_delete BOOLEAN DEFAULT FALSE,
                    send_notification TINYINT(1) DEFAULT 0,
                    send_email TINYINT(1) DEFAULT 0,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (createdBy) REFERENCES ${dbName}.users(id),
                    FOREIGN KEY (roles) REFERENCES ${dbName}.roles(id) ON DELETE SET NULL
                );
            `);



        // -------------- Updated Assessment Users Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.assessment_users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                    assessment_id INT NOT NULL,
                    FOREIGN KEY (assessment_id) REFERENCES ${dbName}.assessments(id) ON DELETE CASCADE,
                    status VARCHAR(25) NULL DEFAULT 'pending',
                    is_completed BOOLEAN DEFAULT FALSE,
                    is_eligible BOOLEAN DEFAULT FALSE,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_passed INT DEFAULT 0
                );
            `);

        // -------------- Updated Assessments Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.assessments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    created_by INT NULL,
                    assessment_name VARCHAR(100) NULL,
                    course_id INT NULL,
                    module_id INT NOT NULL,
                    class_id INT DEFAULT NULL,
                    is_deleted INT DEFAULT 0,
                    duration INT NOT NULL DEFAULT 0,
                    earn_point VARCHAR(10) NULL DEFAULT '0',
                    earn_point_applicable_after VARCHAR(10) NULL DEFAULT '0',
                    is_active BOOLEAN DEFAULT TRUE,
                    is_approved BOOLEAN DEFAULT FALSE,
                    serial_no VARCHAR(45) DEFAULT '0',
                    assessment_type ENUM('assessment', 'survey') DEFAULT 'assessment',
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    pass_percentage INT DEFAULT 60,
                    FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE SET NULL,
                    FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE CASCADE
                );
            `);


        // -------------- Updated Assignment User Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.assignment_user (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    status BOOLEAN DEFAULT FALSE,
                    submit_assignment VARCHAR(255) NULL,
                    user_id INT NOT NULL,
                    assignment_id INT NOT NULL,
                    class_id INT DEFAULT NULL,
                    is_completed BOOLEAN DEFAULT FALSE,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                    FOREIGN KEY (assignment_id) REFERENCES ${dbName}.assignments(id) ON DELETE CASCADE
                );
            `);

        // -------------- Updated Assignments Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.assignments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    assignment_name VARCHAR(255) NOT NULL,
                    question_details TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_by INT NULL,
                    course_id INT NOT NULL,
                    instruction_file_url VARCHAR(255) DEFAULT NULL,
                    class_id INT DEFAULT NULL,
                    is_deleted BOOLEAN DEFAULT FALSE,
                    module_id INT NOT NULL,
                    serial_no VARCHAR(45) DEFAULT '0',
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
                    FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE CASCADE
                );
            `);



        // -------------- Updated Banners Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.banners (
                id INT AUTO_INCREMENT PRIMARY KEY,
                banner_url VARCHAR(255) NOT NULL,
                banner_description VARCHAR(255) NULL,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `);

        // -------------- Updated Certificate Templates Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.certificate_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                template_name VARCHAR(255) NOT NULL,
                template_url TEXT DEFAULT NULL,
                template_fields TEXT DEFAULT NULL,
                image_url TEXT DEFAULT NULL,
                banner_id INT DEFAULT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                is_deleted BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                customised_banner_img VARCHAR(255) DEFAULT NULL,
                FOREIGN KEY (banner_id) REFERENCES ${dbName}.banners(id) ON DELETE SET NULL
            );
        `);


        // -------------- Updated Certificates Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.certificates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                created_by INT NULL,
                user_id INT NOT NULL,
                course_id INT NULL,
                template_id INT NULL,
                certificate_name VARCHAR(255) NOT NULL,
                issued_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expiration_date TIMESTAMP NULL,
                certificate_url VARCHAR(255) NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE SET NULL,
                FOREIGN KEY (template_id) REFERENCES ${dbName}.certificate_templates(id) ON DELETE SET NULL
            );
        `);


        // -------------- Updated Classroom Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                cls_name VARCHAR(100) NULL,
                is_visibility JSON DEFAULT NULL,
                cls_desc VARCHAR(500) NULL,
                is_active BOOLEAN DEFAULT FALSE,
                is_approved BOOLEAN DEFAULT FALSE,
                banner_img VARCHAR(255) DEFAULT NULL,
                collaboration_name VARCHAR(255) DEFAULT NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `);



        // -------------- Updated Classroom Approval Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_approval (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                classroom_id INT NOT NULL,
                is_approval BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE
            );
        `);

        // -------------- Updated Classroom Assessment Questions Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assessment_questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            class_id INT NOT NULL,
            assessment_id INT NOT NULL,
            question_id INT NOT NULL,
            FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
            FOREIGN KEY (assessment_id) REFERENCES ${dbName}.classroom_assessments(id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES ${dbName}.question_bank(id) ON DELETE CASCADE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );`);


        // Set global event scheduler
        await connection.query(`SET GLOBAL event_scheduler = ON`);

        // Create update_assessment_question_count_insert trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
            CREATE TRIGGER update_assessment_question_count_insert
            AFTER INSERT ON ${dbName}.classroom_assessment_questions
            FOR EACH ROW
            BEGIN
                UPDATE ${dbName}.classroom_assessments 
                SET total_questions = total_questions + 1
                WHERE id = NEW.assessment_id AND class_id = NEW.class_id;
            END
        `);

        // Create update_assessment_question_count_delete trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
            CREATE TRIGGER update_assessment_question_count_delete
            AFTER DELETE ON ${dbName}.classroom_assessment_questions
            FOR EACH ROW
            BEGIN
                UPDATE ${dbName}.classroom_assessments 
                SET total_questions = GREATEST(0, total_questions - 1)
                WHERE id = OLD.assessment_id AND class_id = OLD.class_id;
            END
        `);





        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assessment_results (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                class_id INT NOT NULL,
                assessment_id INT NOT NULL,
                total_questions INT NOT NULL,
                total_attempted INT NOT NULL,
                total_correct INT NOT NULL,
                percentage DECIMAL(5,2) NOT NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (assessment_id) REFERENCES ${dbName}.classroom_assessments(id) ON DELETE CASCADE
            );
        `);

        // -------------- Updated Classroom Assessments Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assessments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                class_id INT NOT NULL,
                is_visible BOOLEAN DEFAULT FALSE,
                assessment_name VARCHAR(255) NOT NULL,
                duration INT NOT NULL,
                total_questions INT DEFAULT 0,
                pass_percentage INT DEFAULT 100,
                time_zone VARCHAR(100) DEFAULT NULL,
                activate_time DATETIME DEFAULT NULL,
                deactivate_time DATETIME DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT FALSE,
                is_deleted BOOLEAN DEFAULT FALSE,
                is_time_based BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE
            );
        `);


        // Create assessment activation notification trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
        CREATE TRIGGER assessment_activation_notification
        AFTER UPDATE ON ${dbName}.classroom_assessments
        FOR EACH ROW
        BEGIN
            DECLARE classroom_name VARCHAR(255);
            
            -- Check if assessment was just activated
            IF (OLD.is_active = 0 AND NEW.is_active = 1) THEN
                -- Get classroom name
                SELECT classroom.cls_name INTO classroom_name
                FROM ${dbName}.classroom 
                WHERE classroom.id = NEW.class_id;
                
                -- Only proceed if we found a valid classroom
                IF classroom_name IS NOT NULL THEN
                    -- Get all trainees enrolled in this specific classroom
                    INSERT INTO ${dbName}.notifications (user_id, send_from, title, body, is_deleted, is_read, createdAt, updatedAt)
                    SELECT 
                        classroom_trainee.user_id, 
                        1, -- Admin as default sender
                        'Assessment is Active now', 
                        CONCAT('Assessment "', NEW.assessment_name, '" is now active in classroom "', classroom_name, '"'), 
                        0, 
                        0, 
                        NOW(), 
                        NOW()
                    FROM ${dbName}.classroom_trainee 
                    JOIN ${dbName}.user_roles ON classroom_trainee.user_id = user_roles.user_id
                    WHERE classroom_trainee.class_id = NEW.class_id AND user_roles.role_id = 2;
                END IF;
            END IF;
        END
    `);

        // Create update_assessment_active_status trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
        CREATE TRIGGER update_assessment_active_status
        BEFORE UPDATE ON ${dbName}.classroom_assessments
        FOR EACH ROW
        BEGIN
            IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL THEN
                IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
                    SET NEW.is_active = 1;
                ELSE
                    SET NEW.is_active = 0;
                END IF;
            END IF;
        END
    `);

        // Create update_assessment_active_status_insert trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
        CREATE TRIGGER update_assessment_active_status_insert
        BEFORE INSERT ON ${dbName}.classroom_assessments
        FOR EACH ROW
        BEGIN
            IF NEW.is_time_based = 1 AND NEW.activate_time IS NOT NULL AND NEW.deactivate_time IS NOT NULL THEN
                IF NOW() BETWEEN NEW.activate_time AND NEW.deactivate_time THEN
                    SET NEW.is_active = 1;
                ELSE
                    SET NEW.is_active = 0;
                END IF;
            END IF;
        END
    `);




        // Create update_assessment_active_status_event event
        await connection.query(`USE ${dbName}`);
        await connection.query(
            `DROP EVENT IF EXISTS update_assessment_active_status_event`
        );
        await connection.query(`
        CREATE EVENT update_assessment_active_status_event
        ON SCHEDULE EVERY 2 SECOND
        DO
        BEGIN
            UPDATE ${dbName}.classroom_assessments
            SET is_active = 1
            WHERE is_time_based = 1
            AND activate_time IS NOT NULL
            AND deactivate_time IS NOT NULL
            AND NOW() BETWEEN activate_time AND deactivate_time
            AND is_active = 0;

            UPDATE ${dbName}.classroom_assessments
            SET is_active = 0
            WHERE is_time_based = 1
            AND activate_time IS NOT NULL
            AND deactivate_time IS NOT NULL
            AND (NOW() < activate_time OR NOW() > deactivate_time)
            AND is_active = 1;
        END
    `);

        console.log(`Triggers and events created successfully for ${dbName}`);

        // If everything is successful, commit the transaction
        await connection.query("COMMIT");
        console.log(`Transaction committed successfully for database ${dbName}`);




        // -------------- Updated Classroom Assignment Questions Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assignment_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                assignment_id INT NOT NULL,
                class_id INT NOT NULL,
                question_text TEXT NOT NULL,
                question_type VARCHAR(50) NOT NULL,
                response_type VARCHAR(50) NOT NULL,
                media_url VARCHAR(255) NULL,
                marks INT DEFAULT 0,
                question_order INT DEFAULT 1,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (assignment_id) REFERENCES ${dbName}.classroom_assignments(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        `);





        // -------------- Updated Classroom Assignment Submissions Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assignment_submissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            assignment_id INT NOT NULL,
            user_id INT NOT NULL,
            class_id INT NOT NULL,
            question_id INT NOT NULL,
            submission_url VARCHAR(255) NULL,
            response_type VARCHAR(50) NULL,
            marks_obtained INT NULL,
            answer_text TEXT NULL,
            answer_status ENUM('pending', 'right', 'wrong') DEFAULT 'pending',
            media_url VARCHAR(255) NULL,
            is_completed TINYINT(1) DEFAULT 0,
            assignment_feedback TEXT DEFAULT NULL,
            assignment_evaluation_status BOOLEAN DEFAULT FALSE,
            submittedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN DEFAULT FALSE,
            FOREIGN KEY (assignment_id) REFERENCES ${dbName}.classroom_assignments(id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
            FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES ${dbName}.classroom_assignment_questions(id) ON DELETE CASCADE
            );
        `);

        // -------------- Updated Classroom Assignments Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_assignments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                class_id INT NOT NULL,
                is_visible BOOLEAN DEFAULT FALSE,
                total_marks INT NOT NULL,
                created_by INT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NULL,
                attachment_url VARCHAR(255) NULL,
                time_zone VARCHAR(100) DEFAULT NULL,
                due_date DATETIME DEFAULT NULL,
                assigned_date DATETIME DEFAULT NULL,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        `);


        // -------------- Updated Classroom Live Class Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_live_class (
                id INT AUTO_INCREMENT PRIMARY KEY,
                classroom_id INT NOT NULL,
                live_class_name VARCHAR(60) NOT NULL,
                live_class_description TEXT DEFAULT NULL,
                time_zone VARCHAR(50) DEFAULT NULL,
                class_date DATE DEFAULT NULL,
                start_time DATETIME DEFAULT NULL,
                end_time DATETIME DEFAULT NULL,
                duration VARCHAR(30) DEFAULT NULL,
                status VARCHAR(20) DEFAULT NULL,
                is_started BOOLEAN DEFAULT FALSE,
                host_url TEXT DEFAULT NULL,
                join_url TEXT DEFAULT NULL,
                meeting_id VARCHAR(50) DEFAULT NULL,
                recording_url TEXT DEFAULT NULL,
                created_by INT DEFAULT NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL
            );
        `);

        // -------------- Updated Classroom Question Assessment Answer Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_question_assessment_answer (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                class_id INT NOT NULL,
                assessment_id INT NOT NULL,
                question_id INT NOT NULL,
                answer TEXT NOT NULL,
                is_correct INT DEFAULT 0,
                results_id INT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (assessment_id) REFERENCES ${dbName}.classroom_assessments(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES ${dbName}.question_bank(id) ON DELETE CASCADE,
                FOREIGN KEY (results_id) REFERENCES ${dbName}.classroom_assessment_results(id) ON DELETE SET NULL ON UPDATE CASCADE
            );
        `);

        // -------------- Updated Classroom Resources Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.classroom_resources (
                id INT AUTO_INCREMENT PRIMARY KEY,
                
                classroom_id INT NOT NULL,
                updated_by INT NOT NULL,
                
                resource_name VARCHAR(255) NOT NULL,
                resource_description TEXT,
                
                resource_type ENUM('text', 'image', 'audio', 'video', 'url', 'document') NOT NULL,
                media_url TEXT NULL,
                
                pushed_at TIMESTAMP NULL DEFAULT NULL,
                
                is_active BOOLEAN DEFAULT TRUE,
                is_deleted BOOLEAN DEFAULT FALSE,
                
                seen BOOLEAN DEFAULT FALSE,
                seen_at TIMESTAMP NULL DEFAULT NULL,
                
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                
                FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (updated_by) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
            )
            `);


        // -------------- Updated Classroom Trainee Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.classroom_trainee (
                id INT AUTO_INCREMENT PRIMARY KEY,
                class_id INT NOT NULL,
                user_id INT NOT NULL,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );`);

        // -------------- Updated Classroom Trainer Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.classroom_trainer (
                id INT AUTO_INCREMENT PRIMARY KEY,
                classroom_id INT NOT NULL,
                user_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id),
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id)
            );`);






        // -------------- Updated Course Approval Tables --------------
        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.course_approval (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                is_approval BOOLEAN DEFAULT FALSE,
                is_rejected BOOLEAN DEFAULT FALSE,
                rejected_note TEXT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        `);


        // -------------- Updated Course Approvals Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.course_approvals (
                    course_id INT NOT NULL,
                    approver_id INT NOT NULL,
                    send_approval BOOLEAN DEFAULT 0,
                    PRIMARY KEY (course_id, approver_id),
                    CONSTRAINT fk_course FOREIGN KEY (course_id)
                        REFERENCES ${dbName}.courses(id)
                        ON DELETE CASCADE ON UPDATE CASCADE,
                    CONSTRAINT fk_approver FOREIGN KEY (approver_id)
                        REFERENCES ${dbName}.users(id)
                        ON DELETE CASCADE ON UPDATE CASCADE
                );
                    `);

        // -------------- Updated Course Classroom Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.course_classroom (
                    course_id INT NOT NULL,
                    classroom_id INT NOT NULL,
                    PRIMARY KEY (course_id, classroom_id),
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
                    FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                );
                `);

        // -------------- Updated Course Reviews Tables --------------
        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.course_reviews (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    course_id INT NOT NULL,
                    video_id INT NOT NULL,
                    class_rating INT NOT NULL,
                    mentor_rating INT NOT NULL,
                    review_text TEXT DEFAULT NULL,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE
                );
                `);

        // -------------- Updated Courses Tables --------------
        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.courses (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        created_by VARCHAR(225) NULL,
                        banner_image VARCHAR(255) NULL,
                        course_name VARCHAR(100) NULL,
                        course_desc VARCHAR(500) NULL,
                        course_price VARCHAR(25) NULL,
                        discount_price VARCHAR(25) NULL,
                        currency VARCHAR(10) NULL,
                        course_type VARCHAR(25) NULL,
                        course_category VARCHAR(25) NULL,
                        course_language VARCHAR(50) NULL,
                        course_subcategory TEXT NULL,
                        levels TEXT NULL,
                        validity VARCHAR(255) NULL,
                        total_rating VARCHAR(25) NULL,
                        subscribers VARCHAR(25) NULL,
                        tags JSON,
                        course_info JSON,
                        is_active TINYINT(1) DEFAULT 1,
                        is_approved TINYINT(1) DEFAULT 0,
                        is_deleted TINYINT(1) DEFAULT 0,
                        is_rejected TINYINT(1) DEFAULT 0,
                        send_request TINYINT(1) DEFAULT 0,
                        rejected_note TEXT NULL,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        certificate_template_id INT NULL,
                        FOREIGN KEY (certificate_template_id) REFERENCES ${dbName}.certificate_templates(id) ON DELETE SET NULL
                    );
                `);

        // -------------- Updated Deletion Table --------------
        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.deletion_table (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        execute_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
                    );
                `);

        // -------------- Updated Documents Tables --------------
        await connection.query(`CREATE TABLE IF NOT EXISTS ${dbName}.documents (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    course_id INT NOT NULL,
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
                    module_id INT NULL,
                    created_by INT NULL,
                    FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                    FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE SET NULL,
                    doc_name VARCHAR(128) NOT NULL,
                    doc_url VARCHAR(255) NOT NULL,
                    serial_no VARCHAR(45) DEFAULT '0',
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    is_deleted boolean default false,
                    is_active BOOLEAN DEFAULT TRUE
                    );`);



        await connection.query(`
                        CREATE TABLE IF NOT EXISTS ${dbName}.faqs (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_id INT NULL,
                            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                            question VARCHAR(320) NOT NULL,
                            answer TEXT,
                            is_deleted TINYINT(1) DEFAULT 0,
                            is_active TINYINT(1) DEFAULT 1,
                            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        );
                    `);


        await connection.query(`
                        CREATE TABLE IF NOT EXISTS ${dbName}.group_rooms (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            group_name VARCHAR(255) NOT NULL,
                            class_id INT DEFAULT NULL,
                            created_by INT NULL,
                            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id) ON DELETE SET NULL,
                            FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE SET NULL
                        );
                        `);

        await connection.query(`
                        CREATE TABLE IF NOT EXISTS ${dbName}.group_members (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            group_id INT NOT NULL,
                            user_id INT NOT NULL,
                            is_blocked BOOLEAN DEFAULT FALSE,
                            hide_comments BOOLEAN DEFAULT FALSE,
                            joinedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (group_id) REFERENCES ${dbName}.group_rooms(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
                        );
                        `);

        await connection.query(`
                        CREATE TABLE IF NOT EXISTS ${dbName}.group_messages (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            group_id INT NOT NULL,
                            user_id INT NOT NULL,
                            message TEXT NOT NULL,
                            message_type ENUM('text', 'image', 'video', 'audio', 'document', 'link') DEFAULT 'text',
                            attachment VARCHAR(255) NULL,
                            media_url VARCHAR(500) NULL,
                            media_metadata JSON NULL,
                            emoji_reactions JSON DEFAULT ('{}'),
                            reply_to_message_id INT NULL,
                            likes JSON DEFAULT ('[]'),
                            dislikes JSON DEFAULT ('[]'),
                            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (group_id) REFERENCES ${dbName}.group_rooms(id) ON DELETE CASCADE,
                            FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                            FOREIGN KEY (reply_to_message_id) REFERENCES ${dbName}.group_messages(id) ON DELETE SET NULL
                        );
                        `);


        await connection.query(`
                            CREATE TABLE IF NOT EXISTS ${dbName}.hierarchical_role_permissions (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                role_id INT NOT NULL,
                                module_id INT NOT NULL,
                                action_id INT NOT NULL,
                                is_granted TINYINT(1) DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
                                FOREIGN KEY (module_id) REFERENCES ${dbName}.permission_modules(id) ON DELETE CASCADE,
                                FOREIGN KEY (action_id) REFERENCES ${dbName}.permissions(id) ON DELETE CASCADE
                            );
                            `);




        // live_class
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(255) NOT NULL,
        class_desc VARCHAR(255) DEFAULT NULL,
        time_zone VARCHAR(100) DEFAULT NULL,
        class_date DATE NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        live_class_url VARCHAR(255) DEFAULT NULL,
        zoom_meeting_id VARCHAR(50) DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 0,
        is_deleted TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        class_id INT,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);

        // live_class_logs
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        user_id INT NOT NULL,
        live_class_id INT NOT NULL,
        login_time DATETIME DEFAULT NULL,
        logout_time DATETIME DEFAULT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (live_class_id) REFERENCES ${dbName}.live_class(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);




        // live_class
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(255) NOT NULL,
        class_desc VARCHAR(255) DEFAULT NULL,
        time_zone VARCHAR(100) DEFAULT NULL,
        class_date DATE NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        live_class_url VARCHAR(255) DEFAULT NULL,
        zoom_meeting_id VARCHAR(50) DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 0,
        is_deleted TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        class_id INT,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);

        // live_class_logs
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        user_id INT NOT NULL,
        live_class_id INT NOT NULL,
        login_time DATETIME DEFAULT NULL,
        logout_time DATETIME DEFAULT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (live_class_id) REFERENCES ${dbName}.live_class(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);




        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.hierarchical_role_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_id INT NOT NULL,
            module_id INT NOT NULL,
            action_id INT NOT NULL,
            is_granted TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE ON UPDATE CASCADE,
            FOREIGN KEY (module_id) REFERENCES ${dbName}.permission_modules(id) ON DELETE CASCADE ON UPDATE CASCADE,
            FOREIGN KEY (action_id) REFERENCES ${dbName}.permissions(id) ON DELETE CASCADE ON UPDATE CASCADE
        );
        `);








        // live_class
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(255) NOT NULL,
        class_desc VARCHAR(255) DEFAULT NULL,
        time_zone VARCHAR(100) DEFAULT NULL,
        class_date DATE NOT NULL,
        start_time DATETIME NOT NULL,
        end_time DATETIME NOT NULL,
        live_class_url VARCHAR(255) DEFAULT NULL,
        zoom_meeting_id VARCHAR(50) DEFAULT NULL,
        is_active TINYINT(1) DEFAULT 0,
        is_deleted TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        class_id INT,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);

        // live_class_logs
        await connection.query(`
    CREATE TABLE IF NOT EXISTS ${dbName}.live_class_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        class_id INT NOT NULL,
        user_id INT NOT NULL,
        live_class_id INT NOT NULL,
        login_time DATETIME DEFAULT NULL,
        logout_time DATETIME DEFAULT NULL,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE,
        FOREIGN KEY (live_class_id) REFERENCES ${dbName}.live_class(id) ON DELETE CASCADE ON UPDATE CASCADE
    );
    `);






        await connection.query(`
        CREATE TABLE IF NOT EXISTS ${dbName}.messagebox (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        user_id INT NOT NULL,
        module_id INT NULL,
        FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE SET NULL,
        mentor_id INT NOT NULL,
        FOREIGN KEY (mentor_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES ${dbName}.videos(id) ON DELETE CASCADE,
        stream_time VARCHAR(20) NULL,
        comment VARCHAR(500) NULL,
        likes INT DEFAULT 0,  -- New column to store the number of likes
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );`);



        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.modules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                classroom_id INT NULL,
                module_desc VARCHAR(255) DEFAULT NULL,
                course_id INT NULL,
                module_sequence INT NOT NULL,
                module_name VARCHAR(100) NOT NULL,
                is_deleted BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (classroom_id) REFERENCES ${dbName}.classroom(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE
            );
        `);




        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.mycourses (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                course_id INT NOT NULL,
                class_id INT NULL,
                is_completed BOOLEAN DEFAULT FALSE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE CASCADE,
                FOREIGN KEY (class_id) REFERENCES ${dbName}.classroom(id) ON DELETE SET NULL
            );
        `);






        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.notification_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                mail_notification BOOLEAN DEFAULT TRUE,
                invoice_mail BOOLEAN DEFAULT TRUE,
                push_notification BOOLEAN DEFAULT TRUE,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
            );
        `);





        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            FOREIGN KEY(user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
            send_from VARCHAR(100) NULL,
            title VARCHAR(100) NULL,
            body TEXT NULL,
            course_id INT NULL,
            file_url VARCHAR(255) NULL,
            is_deleted BOOLEAN DEFAULT FALSE,
            is_read BOOLEAN DEFAULT FALSE,
            createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE SET NULL ON UPDATE CASCADE
            );`);










        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    send_from VARCHAR(100) NULL,
                    title VARCHAR(100) NULL,
                    body TEXT NULL,
                    course_id INT NULL,
                    file_url VARCHAR(255) NULL,
                    is_deleted BOOLEAN DEFAULT FALSE,
                    is_read BOOLEAN DEFAULT FALSE,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                    FOREIGN KEY (course_id) REFERENCES ${dbName}.courses(id) ON DELETE SET NULL ON UPDATE CASCADE
                );
            `);



        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.organization (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    org_name VARCHAR(50) NOT NULL,
                    db_name VARCHAR(255) NOT NULL UNIQUE,
                    org_image VARCHAR(255) NULL,
                    org_email VARCHAR(255) NOT NULL UNIQUE,
                    
                    org_phone BIGINT NULL,
                    org_desc TEXT NULL,
                    
                    country VARCHAR(50) NULL,
                    state VARCHAR(255) NULL,
                    zipcode VARCHAR(10) NULL,
                    city VARCHAR(255) NULL,
                    
                    authorized_person_name VARCHAR(255) NOT NULL,
                    authorized_person_identity VARCHAR(255) NOT NULL,
                    authorized_person_phone BIGINT NOT NULL,
                    authorized_person_email VARCHAR(255) NOT NULL UNIQUE,
                    
                    require_doc_url VARCHAR(255) NOT NULL,
                    subscription_plan VARCHAR(255) NOT NULL,
                    max_users INT NULL,
                    
                    payment_card_number BIGINT NULL,
                    payment_expiry_date CHAR(4) NULL,
                    payment_cvv SMALLINT NULL,
                    billing_address VARCHAR(255) NULL,
                    
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    is_verified BOOLEAN DEFAULT TRUE,
                    is_deleted BOOLEAN DEFAULT FALSE
                );
            `);




        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.orginfo (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    created_by VARCHAR(255) NULL,
                    name VARCHAR(255) NOT NULL,
                    description TEXT NULL,
                    is_deleted BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                );
            `);



        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.otps (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(320) NOT NULL,
                    otp VARCHAR(6) NOT NULL,
                    otp_expire_time TIMESTAMP NULL
                );
            `);




        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.payments (
                    id INT NOT NULL AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(10) NOT NULL DEFAULT 'USD',
                    status ENUM('pending','completed','failed','refunded') NOT NULL DEFAULT 'pending',
                    payment_method VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    payment_uid TEXT,
                    course_name VARCHAR(255) DEFAULT NULL,
                    discount_point DECIMAL(10,2) DEFAULT NULL,
                    receipt_url VARCHAR(255) DEFAULT NULL,
                    receipt_pdf_path VARCHAR(255) DEFAULT NULL,
                    PRIMARY KEY (id),
                    KEY user_id (user_id),
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE
                );
            `);






        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.permission_modules (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(100) NOT NULL,
                    parent_id INT DEFAULT NULL,
                    \`order\` SMALLINT DEFAULT 0,
                    is_active TINYINT(1) DEFAULT 1,
                    icon VARCHAR(50) DEFAULT NULL,
                    description TEXT DEFAULT NULL,
                    is_deleted TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES ${dbName}.permission_modules(id) ON DELETE SET NULL
                );
            `);




        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.permission_modules_with_new (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    slug VARCHAR(100) NOT NULL,
                    parent_id INT DEFAULT NULL,
                    \`order\` SMALLINT DEFAULT 0,
                    is_active TINYINT(1) DEFAULT 1,
                    icon VARCHAR(50) DEFAULT NULL,
                    description TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES ${dbName}.permission_modules_with_new(id) ON DELETE SET NULL
                );
            `);







        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.question_answer (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    question_id INT NOT NULL,
                    user_id INT NOT NULL,
                    answer TEXT NULL,
                    collect_point VARCHAR(255) NOT NULL,
                    is_correct BOOLEAN DEFAULT FALSE,
                    assessment_id INT NULL,
                    permanent_answer TEXT NULL,
                    question_type VARCHAR(255) NULL,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (question_id) REFERENCES ${dbName}.questions(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                    FOREIGN KEY (assessment_id) REFERENCES ${dbName}.assessments(id) ON DELETE CASCADE
                );
            `);





        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.question_bank (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    question_name TEXT NOT NULL,
                    question_type ENUM('mcq') NOT NULL,
                    subject VARCHAR(100) NULL,
                    module VARCHAR(100) NULL,
                    tags LONGTEXT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_deleted BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                );
            `);


        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.question_bank_options (
                id INT AUTO_INCREMENT PRIMARY KEY,
                question_id INT NOT NULL,
                option_number INT NOT NULL,
                option_value TEXT NOT NULL,
                is_correct BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                is_deleted BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (question_id) REFERENCES ${dbName}.question_bank(id) ON DELETE CASCADE
            );`);




        await connection.query(`
                CREATE TABLE IF NOT EXISTS ${dbName}.question_options (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    question_id INT NOT NULL,
                    option_key VARCHAR(50) NOT NULL,
                    content_type ENUM('text', 'image', 'audio') NOT NULL DEFAULT 'text',
                    content_value TEXT NOT NULL,
                    media_url VARCHAR(255) NULL,
                    text_fallback TEXT NULL,
                    is_correct INT NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (question_id) REFERENCES ${dbName}.questions(id) ON DELETE CASCADE
                );`);








        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.questions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        assessment_id INT NOT NULL,
                        module_id INT NOT NULL,
                        question TEXT NOT NULL,
                        question_content_type ENUM('text', 'image', 'audio') NOT NULL DEFAULT 'text',
                        question_media_url VARCHAR(255) NULL,
                        ref_video_id INT NULL,
                        video_timestamp VARCHAR(10) NULL,
                        options TEXT NULL,
                        correct_option VARCHAR(250) DEFAULT '[]',
                        question_type ENUM('mcq', 'normal', 'written') NOT NULL,
                        rank_point INT NOT NULL DEFAULT 0,
                        collect_point INT NOT NULL DEFAULT 0,
                        is_deleted TINYINT(1) DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (assessment_id) REFERENCES ${dbName}.assessments(id) ON DELETE CASCADE,
                        FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE CASCADE,
                        FOREIGN KEY (ref_video_id) REFERENCES ${dbName}.videos(id) ON DELETE SET NULL ON UPDATE CASCADE
                    );
                `);









        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.rankings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        score VARCHAR(5) NOT NULL,
                        total_rank INT NOT NULL,
                        start_rating INT NULL,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    );
                `);

        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.recent_videos (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        video_id INT NOT NULL,
                        completed_stream VARCHAR(50) NOT NULL,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        is_complete BOOLEAN DEFAULT FALSE,
                        serial_no VARCHAR(45) DEFAULT '0',
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                        FOREIGN KEY (video_id) REFERENCES ${dbName}.videos(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_user_video (user_id, video_id)
                    );
                `);




        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.replies_comments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        comment_id INT NOT NULL,
                        user_id INT NOT NULL,
                        reply_comment TEXT NOT NULL,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (comment_id) REFERENCES ${dbName}.messagebox(id) ON DELETE CASCADE ON UPDATE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE ON UPDATE CASCADE
                    );
                `);



        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.role_permissions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        role_id INT NOT NULL,
                        permission_id INT NOT NULL,
                        is_granted TINYINT(1) DEFAULT 1,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (role_id) REFERENCES ${dbName}.roles(id) ON DELETE CASCADE,
                        FOREIGN KEY (permission_id) REFERENCES ${maindb}.permissions(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_role_permission (role_id, permission_id)
                    );
                `);






        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.roles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL UNIQUE,
                    description VARCHAR(255) NULL,
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_deleted boolean default false,
                    is_active boolean default true,
                    role_type ENUM('admin', 'trainee') NOT NULL DEFAULT 'admin',
                    created_by int,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    CONSTRAINT fk_created_by FOREIGN KEY (created_by) REFERENCES ${dbName}.users(id)
                 );
                `);




        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.student_answer (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        question_id INT NOT NULL,
                        answer TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                        FOREIGN KEY (question_id) REFERENCES ${dbName}.questions(id) ON DELETE CASCADE
                    );
                `);



        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.Notes (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        video_id INT NOT NULL,
                        user_id INT NOT NULL,
                        module_id INT NULL,
                        stream_time VARCHAR(20) NULL,
                        note LONGTEXT NOT NULL,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (module_id) REFERENCES ${dbName}.modules(id) ON DELETE SET NULL,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                        FOREIGN KEY (video_id) REFERENCES ${dbName}.videos(id) ON DELETE CASCADE
                    );
                `);




        await connection.query(`
                    CREATE TABLE IF NOT EXISTS ${dbName}.user_reactions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        group_id INT NOT NULL,
                        comment_id INT NOT NULL,
                        user_id INT NOT NULL,
                        is_like BOOLEAN NOT NULL,
                        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (group_id) REFERENCES ${dbName}.group_rooms(id) ON DELETE CASCADE,
                        FOREIGN KEY (comment_id) REFERENCES ${dbName}.group_messages(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES ${dbName}.users(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_user_reaction (user_id, comment_id)
                    );
                `);



        await connection.query(`
            CREATE TABLE IF NOT EXISTS ${dbName}.question_tags (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            tag_name VARCHAR(100) NULL,
            created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP);
        `);







        // Now create all triggers and events after all tables exist
        console.log(`Creating triggers and events for ${dbName}...`);

        await connection.query(`USE ${dbName}`);

        await connection.query(`SET GLOBAL event_scheduler = ON`);

        // Create course approval notification trigger
        await connection.query(`USE ${dbName}`);
        await connection.query(`
        CREATE TRIGGER course_approval_notification
        AFTER UPDATE ON ${dbName}.courses
        FOR EACH ROW
        BEGIN
            -- Check if course was just approved and activated
            IF (OLD.is_approved = 0 AND NEW.is_approved = 1 AND NEW.is_active = 1) THEN
                -- Insert notification for all trainees (role_id = 2)
                INSERT INTO ${dbName}.notifications (
                    user_id,
                    send_from,
                    title,
                    body,
                    course_id,
                    is_deleted,
                    is_read,
                    createdAt,
                    updatedAt
                )
                SELECT 
                    u.id,
                    NEW.created_by,
                    'New Course Available',
                    CONCAT('Course "', NEW.course_name, '" is now available for enrollment'),
                    NEW.id, 
                    0,
                    0,
                    NOW(),
                    NOW()
                FROM ${dbName}.users u
                JOIN ${dbName}.user_roles ur ON u.id = ur.user_id
                WHERE ur.role_id = 2;
            END IF;
        END
    `);






        return true;
    } catch (error) {
        // If any error occurs, rollback the transaction
        await connection.query("ROLLBACK");
        console.error(`Error creating organization database: ${error.message}`);
        throw error;
    } finally {
        // Release the connection back to the pool
        connection.release();
    }
}

module.exports = { createOrganizationDatabase };
