import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import logo from '../../assets/images/logo/logo.png';
import { logoutApi } from '../../services/authService';

function SideBar() {
  const location = useLocation();
  const navigate = useNavigate();

  // State for toggle collapse in menu items
  const [collapsed, setCollapsed] = useState(false);
  // State for mobile sidebar visibility
  const [showMobileSidebar, setShowMobileSidebar] = useState(false);
  // Track if we're on mobile or not
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // Check screen size on mount and resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-hide sidebar when resizing to desktop
      if (window.innerWidth >= 768) {
        setShowMobileSidebar(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle mobile sidebar
  const toggleMobileSidebar = () => {
    setShowMobileSidebar(!showMobileSidebar);
  };

  const isActive = (path) => {
    if (path === '/user/courses' && (
      location.pathname.startsWith('/user/courses/courseDetails') ||
      location.pathname.startsWith('/user/courses/orderDetails') ||
      location.pathname.startsWith('/user/courses/successPayment') ||
      location.pathname.startsWith('/user/courses/Result') ||
      location.pathname.startsWith('/user/courses/WatchCourse') ||
      location.pathname.startsWith('/user/resultvideo')
    )) {
      return 'active';
    }
    if (path === '/user/AllClassroom' && (
      location.pathname.startsWith('/user/AllClassroom/classroomDetails') ||
      location.pathname.startsWith('/user/assignmentsQuestions')
    )) {
      return 'active';
    }
    return location.pathname === path ? 'active' : '';
  };

  const handleLinkClick = () => {
    // Close sidebar on mobile when clicking a link
    if (isMobile) {
      setShowMobileSidebar(false);
    }
  };

  const handleLogout = async () => {
    try {
      // Optionally call logout API
      await logoutApi();

      // Clear all local storage items
      localStorage.clear();

      // Redirect to login page
      navigate('/');
    } catch (error) {
      console.error("Logout error:", error);
      localStorage.clear();
      navigate('/');
    }
  };


  // Mobile toggle button to be rendered in the layout
  const MobileToggleButton = () => (
    <button
      className="btn p-0"
      onClick={toggleMobileSidebar}
      aria-label="Toggle sidebar"
      style={{ border: 'none', background: 'none' }}
    >
      <Icon icon="mdi:menu" width="32" height="32" color="#212529" />
    </button>
  );

  // Standard sidebar content
  const SidebarContent = () => (
    <div className="sidebar-nav py-2">
      <Link
        to="/user/dashboard"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/dashboard') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:grid-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Dashboard</span>
      </Link>

      <Link
        to="/user/profile"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/profile') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:person-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Profile</span>
      </Link>

      <Link
        to="/user/courses"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/courses') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:book-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Courses</span>
      </Link>

      <Link
        to="/user/AllClassroom"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/AllClassroom') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:desktop-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Classrooms</span>
      </Link>

      <Link
        to="/user/certificates"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/certificates') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:document-ribbon-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Certificates</span>
      </Link>

      {/* Notifications */}
      <Link
        to="/user/notifications"
        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/notifications') ? 'active' : ''}`}
        onClick={handleLinkClick}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:alert-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Notifications</span>
      </Link>

      {/* Logout */}
      <div
        className="nav-item d-flex align-items-center py-2 px-3 text-danger"
        onClick={handleLogout}
        role="button"
        style={{ cursor: 'pointer' }}
      >
        <div className="nav-icon-wrapper me-3 d-flex align-items-center justify-content-center">
          <Icon icon="fluent:sign-out-24-regular" width="22" height="22" />
        </div>
        <span className="nav-text">Logout</span>
      </div>

      {/* Help Center Card */}
      <div 
        className="mx-auto mt-4 p-3 rounded-3 text-center" 
        style={{ 
          backgroundColor: '#f0f2ff',
          width: '320px',
          maxWidth: '100%'
        }}
      >
        <div className="mb-2">
          <Icon icon="fluent:question-circle-24-regular" width="28" height="28" color="#4361ee" />
        </div>
        <h6 className="mb-1" style={{ fontSize: '14px' }}>Help center</h6>
        <p className="small mb-2" style={{ color: '#6b7280', fontSize: '12px' }}>
          Any problem?<br />
          send us a message
        </p>
        <Link 
          to="/user/help"
          className="btn btn-primary btn-sm w-100"
          style={{ 
            backgroundColor: '#4361ee',
            border: 'none',
            borderRadius: '6px',
            padding: '6px 12px',
            fontSize: '13px'
          }}
        >
          Go to help center
        </Link>
      </div>
    </div>
  );

  return (
    <>
      {/* Logo for desktop view */}
      {!isMobile && (
        <div className="logo-container border-end" style={{ backgroundColor: '#f6f7f9', height: '92px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0.75rem' }}>
          <Link to="/user/dashboard" className="text-decoration-none d-flex justify-content-center">
            <img
            src={logo} 
              alt="Logo"
              className="img-fluid"
              style={{ maxHeight: '100px', maxWidth: '100%' }}
            />
          </Link>
        </div>
      )}

      {/* Mobile Toggle Button - To be rendered in the layout */}
      {isMobile && <MobileToggleButton />}

      {/* Mobile Sidebar Overlay */}
      {showMobileSidebar && (
        <div
          className="d-md-none"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            zIndex: 1030
          }}
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Mobile Sidebar */}
      {isMobile && (
        <div
          className={`d-md-none mobile-sidebar border-end position-fixed ${showMobileSidebar ? 'show' : 'hide'}`}
          style={{
            width: '280px',
            height: '100vh',
            top: 0,
            left: 0,
            zIndex: 1040,
            transform: showMobileSidebar ? 'translateX(0)' : 'translateX(-100%)',
            transition: 'transform 0.3s ease-in-out'
          }}
        >
          {/* Mobile Logo */}
          <div className="logo-container border-bottom" style={{ height: '78px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0.75rem 1rem' }}>
            <Link to="/user/dashboard" className="text-decoration-none">
              <img
                src={logo}
                alt="Logo"
                className="img-fluid"
                style={{ maxHeight: '50px', maxWidth: '100%' }}
              />
            </Link>
            <button
              className="btn btn-sm btn-outline-secondary rounded-circle"
              onClick={toggleMobileSidebar}
              aria-label="Close menu"
            >
              <Icon icon="fluent:dismiss-24-filled" width="20" height="20" />
            </button>
          </div>
          <div className="overflow-auto" style={{ height: 'calc(100vh - 78px)' }}>
            <SidebarContent />
          </div>
        </div>
      )}

      {/* Desktop Sidebar Content */}
      {!isMobile && (
        <div className="sidebar overflow-auto" style={{ minHeight: 'calc(100vh - 120px)' }}>
          <SidebarContent />
        </div>
      )}
    </>
  );
}

export default SideBar;