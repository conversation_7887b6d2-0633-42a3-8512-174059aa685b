import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import './Notifications.css';
import { toast } from 'react-toastify';
import { newGetNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '../../../services/userService';
import NoData from '../../../components/common/NoData';
import moment from 'moment-timezone';

function Notifications() {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState([]);
  const [filteredNotifications, setFilteredNotifications] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await newGetNotifications();
      console.log('Notifications response:', response);

      if (response && response.notifications) {
        setNotifications(response.notifications);
        setFilteredNotifications(response.notifications);
      } else {
        setNotifications([]);
        setFilteredNotifications([]);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('Failed to load notifications');
      toast.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  const handleSearch = (e) => {
    const searchValue = e.target.value.toLowerCase();
    setSearchTerm(searchValue);

    if (!searchValue.trim()) {
      setFilteredNotifications(notifications);
      return;
    }

    const filtered = notifications.filter(notification => {
      const title = notification.title?.toLowerCase() || '';
      const body = notification.body?.toLowerCase() || '';
      return title.includes(searchValue) || body.includes(searchValue);
    });

    setFilteredNotifications(filtered);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleNotificationClick = async (notification) => {
    try {
      const response = await markNotificationAsRead({ notificationId: notification.id });

      if (response.success) {
        const updatedNotifications = notifications.map(n =>
          n.id === notification.id ? { ...n, is_read: true } : n
        );
        setNotifications(updatedNotifications);
        setFilteredNotifications(updatedNotifications);
      }

      navigate(`/admin/notifications/notificationDetails`, {
        state: { notification }
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
      navigate(`/admin/notifications/notificationDetails`, {
        state: { notification }
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await markAllNotificationsAsRead();

      if (response.success) {
        const updatedNotifications = notifications.map(notification => ({
          ...notification,
          is_read: true
        }));
        setNotifications(updatedNotifications);
        setFilteredNotifications(updatedNotifications);
        toast.success('All notifications marked as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  if (loading) {
    return (
      <div className="notifications-container">
        <div className="text-center p-5">
          Loading notifications...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="notifications-container">
        <div className="text-center p-5 text-danger">
          {error}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="row align-items-center mb-2">
        <div className="col-12 col-md-6">
          <div className="search-control">
            <input
              type="text"
              className="form-control search-input w-50"
              placeholder="Search notifications..."
              aria-label="Search notifications"
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>
        <div className="col-12 col-md-6 text-end">
        <button
            className="btn btn-primary w-50"
            onClick={handleMarkAllAsRead}
            disabled={!notifications.some(n => !n.is_read)}
          >
            <span className='d-none d-md-block'>
              <Icon icon="mdi:check-all" width="20" height="20" />&nbsp;
            </span>
            Mark all as read
          </button>
        </div>
      </div>

      <div className="row">
        <div className="container">
          <div className="notifications-container p-0 p-md-2">
            <div className="notifications-list">
              {Array.isArray(filteredNotifications) && filteredNotifications.length > 0 ? (
                filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                    role="button"
                    tabIndex={0}
                  >
                    <div className="notification-content">
                      <div className="notification-title">
                        <h3>{notification.title}</h3>
                        {!notification.is_read && (
                          <span className="unread-badge">New</span>
                        )}
                      </div>
                      <p className="notification-body">{notification.body}</p>
                      <div className="notification-meta">
                        <span className="notification-date">
                          <Icon icon="mdi:clock-outline" width="16" height="16" />
                          <span>{moment.utc(notification.createdAt).local().format('MMM DD, YYYY, hh:mm A')}</span>

                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <NoData message={searchTerm ? "No notifications found matching your search." : "No notifications found."} />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default Notifications;
