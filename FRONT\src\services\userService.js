import Cummunity from '../pages/user/classroom/Cummunity';
import { GET, POST, UPLOAD_FILE , PUT, DELETE } from './apiController';


  // Performance dashboard
  export const getDashboardMonthlyPerformance = () => {
    return GET("/org/dashboard_performance");
  };
  


export const getTraineeStats = () => {
    return GET("/org/user_dashboard_status");
  };
  

// Activity
  export const getUserLogs = () => {
    return GET("/org/getUserLogs");
  };


  // /get_task_lists
  export const getTaskLists = (data) => {
    return POST("/org/get_task_lists", data);
  };

  // /mark_task_as_seen
  export const markTaskAsSeen = (data) => {
    return PUT("/org/mark_task_as_seen", data);
  };




// ------------------------------------------- Profile 

// Get Profile
export const getProfile = () => {
  return GET("/org/get_profile");
};

export const editProfile = (data) => {
  return POST("/org/edit_profile", data);
};

export const updateProfilePic = (data) => {
  // cosnel form data 
  // console.log('Data:--------------', data.get('profileImage'));

  return UPLOAD_FILE("/org/update_profile_pic", data);
};

export const changePassword = (data) => {
  return POST("/org/change_password", data);
};





// -------------------------------------------------- certificates 

export const getTraineeCertificates = () => {
  return GET("/org/get_certificates");
};



// -------------------------------------------------- notifications 

// Notification
export const newGetNotifications = async () => {
  try {
    const response = await POST(`/org/new_get_notifications`);
    return response.data;
  } catch (error) {
    console.error('Error in newGetNotifications:', error);
    throw error;
  }
};

export const markNotificationAsRead = (data) => {
  return POST("/org/mark_notification_as_read", data);
};

export const markAllNotificationsAsRead = () => {
  return POST("/org/mark_all_notifications_as_read");
};

export const getCourseDetailsByIdForNotification = (data) => {
  return POST(`/mainOrg/course_details_for_notification`, data);
};



// ----------------------------------------------------- settings 

// Account Settings
export const updateNotificationSettings = (data) => {
  return PUT("/org/update_notification_settings", data);
};

export const getNotificationSettings = () => {
  return GET(`/mainOrg/get_notification_settings_org`);
};

// Delete Account
export const deleteUserAccount = (data) => {
  return POST("/org/delete_account", data);
};

// soft_delete_account
export const softDeleteAccount = (data) => {
  return POST("/org/soft_delete_account", data);
};

// ------------------------------------------------------- help 

export const getFAQ = () => {
  return GET("/org/get_faqs");
};

export const getAllTicket = (data) => {
  return POST("/org/get_all_raise_ticket", data);
};

export const postRaiseTicketV2 = (data) => {
  return POST("/org/raise_ticket_v2", data);
};

export const deleteTicket = (ticket_id) => {
  return DELETE(`/org/delete_ticket/${ticket_id}`);
};

export const getAboutUs = () => {
  return GET(`/org/get_orginfo`);
};




// ------------------------------------------------------- courses 



// Courses
export const allCourses = (data) => {
  return POST("org/get_all_course", data);
};


export const getMetadataCourseDetails = (data) => {
  return POST("org/get_course_details", data);
};



export const getMyCourses = (data) => {
  return POST("/org/get_my_course", data);
};



export const AddMyCourses = (data) => {
  return GET(`/org/add_to_mycourse?course_id=${data.course_id}`);
};




export const getModuleList = (data) => {
  return POST("/org/get_module_list", data);
};

export const getCourseCompletePercentage = (course_id, video_id = null) => {
  return GET(`/org/course_completion/${course_id}/${video_id}`);
};



// Survey start
export const getCourseSurvey = (data) => {
  return POST("/org/get_survey_questions", data);
};
export const submitCourseSurvey = (data) => {
  return POST("/org/submit_survey_answers", data);
};



export const getNotes = (video_id) => {
  return GET(`/org/get_notes_details?video_id=${video_id}`);
};

export const addNote = (data) => {
  return POST("/org/add_note", data);
};

export const deleteNotes = (noteId) => {
  return DELETE(`/org/delete_note/${noteId}`);
};
export const updateNote = (data) => {
  return POST("/org/edit_note", data);
};




export const getComments = (video_id) => {
  return GET(`/org/get_comments_details?video_id=${video_id}`);
};

export const addComment = (data) => {
  return POST("/org/add_comment", data);
};

export const deleteComment = (comment_id) => {
  return DELETE(`/org/delete_comment/${comment_id}`);
};

// /update_comment
export const updateComment = (data) => {
  return POST("/org/update_comment", data);
};


export const getProfileComments = (user_id) => {
  return GET(`/org/get_profile_comments/${user_id}`);
};







export const getCourseReviews = (course_id, video_id) => {
  const queryParams = video_id
    ? `course_id=${course_id}&video_id=${video_id}`
    : `course_id=${course_id}`;
  return GET(`/org/get_course_reviews?${queryParams}`);
};

// Rating
export const addCourseReview = (data) => {
  return POST("/org/add_course_review", data);
};

// edit_course_review
export const editCourseReview = (data) => {
  return POST("/org/edit_course_review", data);
};


export const deleteReview = (review_id) => {
  return DELETE(`/org/delete_review/${review_id}`);
};


// In endpoints.js

const endpoint = "/mainOrg";
export const checkCoursePurchase = (data) => {
  return POST(endpoint + `/check_course_purchase`, data);
};


export const processPayment = (data) => {
  return POST(`/org/process_payment`, data);
};

export const successPayment = (course_id, session_id) => {
  return GET(
    `/org/success_payment?course_id=${course_id}&session_id=${session_id}`
  );
};


 
export const getQuestionsAsCourseQuiz = (data) => {
  return POST("/org/get_questions", data);
};




// ---------------------------------- course assesment 

// /get_course_assessment
export const getCourseAssessment = (data) => {
  return POST("/org/get_course_assessment_by_assessment_id", data);
};




// /submit_assessment_with_result
export const submitAssessmentWithResult = (data) => {
  return POST("/org/submit_assessment_with_result", data);
};



// getAssessmentResultsDetailed
export const getAssessmentResultsDetailed = (assessmentId) => {
  return GET(`/org/get_assessment_results_detailed/${assessmentId}`);
};

// get_video_by_id
export const getVideoById = (data) => {
  return GET("/org/get_video_by_id",data);
};


// Like Comment 
export const updateLike = (commentId) => {
  return POST("/org/update_like", { commentId });
};
// Reply Comments
export const addReplyComment = (data) => {
  return POST("/org/add_reply_comment", data);
};
export const getRepliesByCommentId = (data) => {
  return POST("/org/get_replies_by_comment_id", data);
};




export const deleteReplyComment = (reply_id) => {
  return POST(`/org/delete_reply_comment`, { reply_id });
};

// /update_reply_comment 
export const updateReplyComment = (data) => {
  return POST("/org/update_reply_comment", data);
};



// -------------------------------------------------- Classroom 

export const getTraineeClassrooms = (data) => {
  return POST(`/org/classroom/trainee/classrooms`, data);
};





export const getTraineeClassroomsDashboardData = (data) => {
  return POST(`/org/classroom/dashboard`, data);
};


// Assignment
export const getAssignments = (data) => {
  return POST(`/org/classroom/user_assignments`, data);
};

export const getUserClassroomAssignmentsQuestions = (data) => {
  return POST(
    endpoint + `/classroom/get_user_classroom_assignments_questions`,
    data
  );
};

export const submitUserClassroomAssignmentResponse = (data) => {
  if (data instanceof FormData) {
    return UPLOAD_FILE(
      endpoint + `/classroom/submit_user_classroom_assignment_response`,
      data
    );
  } else {
    return POST(
      endpoint + `/classroom/submit_user_classroom_assignment_response`,
      data
    );
  }
};




// /org/classroom/trainee_assessments
export const getAllAssessmentToClassroomInUser = (data) => {
  return POST(`/org/classroom/trainee_assessments`, data);
};


export const getAllQuestionsForClassroomAssessmentForUser = (data) => {
  return POST(`/org/classroom/assessment/questions`, data);
};

// /org/classroom/assessment/submit
export const submitClassroomAssessment = (data) => {
  return POST(`/org/classroom/assessment/submit`, data);
};

// /get_all_resources
export const getAllResources = (data) => {
  return POST(`/org/get_all_resources`, data);
};

// /mark_resource_as_seen
export const markResourceAsSeen = (data) => {
  return POST(`/org/mark_resource_as_seen`, data);
};


// --- Cummunity
// Profile
export const viewProfile = () => {
  return GET("/org/get_profile");
};


// -------------------------Task 

export const getAllTasksByUser = () => {
  return GET("/org/get_all_tasks");
};

export const addTask = (data) => {
  return POST("/org/add_task", data);
};

export const editTaskById = (data) => {
  return PUT("/org/edit_task", data);
};

export const deleteTaskById = (task_id) => {
  return DELETE(`/org/delete_task?task_id=${task_id}`);
};


export const markTaskAsCompleted = (data) => {
  return PUT("/org/mark_task_as_completed", data);
};





// -------------------------- payment 
// /get_user_transactions
export const getPaymentHistory = () => {
  return GET("/org/get_user_transactions");
};






export const saveRecentVideo = (data) => {
  return POST("/org/save_recent_video", data);
};

//generate_certificate
export const GenerateCertificate = (data) => {
  return POST("/org/generate_certificate", data);
};




// ----------------------- resources
// /get_classroom_resources_for_trainees"
export const getClassroomResourcesForTrainees = (data) => {
  return POST(`/org/get_classroom_resources_for_trainees`, data);
};

// --------------------------------- Zoom Meeting SDK Live Classes (Trainee)

// Get all live classes for a classroom (trainee view)
export const getTraineeLiveClasses = (data) => {
  return POST("/mainOrg/classroom/zoom_live_classes", data);
};

// Generate join token for trainee to join meeting
export const generateTraineeJoinToken = (data) => {
  return POST("/mainOrg/classroom/zoom_live_class/join_token", data);
};
