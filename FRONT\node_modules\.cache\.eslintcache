[{"C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx": "4", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx": "5", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx": "6", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx": "7", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx": "8", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx": "9", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx": "10", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx": "11", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx": "12", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx": "13", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx": "14", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx": "15", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx": "16", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx": "17", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx": "18", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx": "19", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx": "20", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx": "21", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx": "22", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx": "23", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx": "24", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx": "25", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx": "26", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx": "27", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx": "28", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx": "29", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx": "30", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx": "31", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx": "32", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx": "33", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx": "34", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx": "35", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx": "36", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx": "37", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx": "38", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx": "39", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx": "40", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx": "41", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx": "42", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx": "43", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx": "44", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx": "45", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx": "46", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx": "47", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx": "48", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx": "49", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx": "50", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx": "51", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx": "52", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx": "53", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx": "54", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx": "55", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx": "56", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx": "57", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx": "58", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx": "59", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx": "60", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx": "61", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx": "62", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx": "63", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx": "64", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx": "65", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx": "66", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx": "67", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx": "68", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx": "69", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx": "70", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx": "71", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx": "72", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx": "73", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx": "74", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx": "75", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx": "76", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx": "77", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx": "78", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js": "79", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js": "80", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js": "81", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx": "82", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js": "83", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx": "84", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx": "85", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx": "86", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx": "87", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx": "88", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx": "89", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx": "90", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx": "91", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx": "92", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx": "93", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx": "94", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx": "95", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js": "96", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx": "97", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx": "98", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx": "99", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx": "100", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx": "101", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx": "102", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx": "103", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx": "104", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx": "105", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx": "106", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx": "107", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx": "108", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js": "109", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx": "110", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx": "111", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx": "112", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx": "113", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx": "114", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx": "115", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js": "116", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx": "117", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx": "118", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx": "119", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx": "120", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx": "121", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx": "122", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx": "123", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx": "124", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx": "125", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx": "126", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx": "127", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx": "128", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx": "129", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx": "130", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx": "131", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx": "132", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx": "133", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx": "134", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx": "135", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx": "136", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js": "137", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx": "138", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx": "139", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx": "140", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx": "141", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx": "142", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx": "143", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx": "144", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx": "145", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx": "146", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx": "147", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx": "148", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js": "149", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx": "150", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx": "151", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx": "152", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx": "153", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx": "154", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js": "155", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx": "156", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx": "157", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx": "158", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx": "159"}, {"size": 552, "mtime": *************, "results": "160", "hashOfConfig": "161"}, {"size": 375, "mtime": *************, "results": "162", "hashOfConfig": "161"}, {"size": 1633, "mtime": *************, "results": "163", "hashOfConfig": "161"}, {"size": 7498, "mtime": *************, "results": "164", "hashOfConfig": "161"}, {"size": 4155, "mtime": *************, "results": "165", "hashOfConfig": "161"}, {"size": 1522, "mtime": *************, "results": "166", "hashOfConfig": "161"}, {"size": 2212, "mtime": *************, "results": "167", "hashOfConfig": "161"}, {"size": 2266, "mtime": *************, "results": "168", "hashOfConfig": "161"}, {"size": 8501, "mtime": *************, "results": "169", "hashOfConfig": "161"}, {"size": 5704, "mtime": *************, "results": "170", "hashOfConfig": "161"}, {"size": 14941, "mtime": *************, "results": "171", "hashOfConfig": "161"}, {"size": 5494, "mtime": 1753080196472, "results": "172", "hashOfConfig": "161"}, {"size": 4410, "mtime": 1753083304579, "results": "173", "hashOfConfig": "161"}, {"size": 9645, "mtime": 1752674149451, "results": "174", "hashOfConfig": "161"}, {"size": 882, "mtime": 1750498842000, "results": "175", "hashOfConfig": "161"}, {"size": 887, "mtime": 1750500968000, "results": "176", "hashOfConfig": "161"}, {"size": 23107, "mtime": 1753190819653, "results": "177", "hashOfConfig": "161"}, {"size": 64676, "mtime": 1753170429987, "results": "178", "hashOfConfig": "161"}, {"size": 53481, "mtime": 1752484548147, "results": "179", "hashOfConfig": "161"}, {"size": 65216, "mtime": 1752490834024, "results": "180", "hashOfConfig": "161"}, {"size": 4643, "mtime": 1752409801600, "results": "181", "hashOfConfig": "161"}, {"size": 4216, "mtime": 1752409801605, "results": "182", "hashOfConfig": "161"}, {"size": 40073, "mtime": 1752409801612, "results": "183", "hashOfConfig": "161"}, {"size": 36784, "mtime": 1752409801613, "results": "184", "hashOfConfig": "161"}, {"size": 51452, "mtime": 1753276336843, "results": "185", "hashOfConfig": "161"}, {"size": 12687, "mtime": 1751710504000, "results": "186", "hashOfConfig": "161"}, {"size": 169, "mtime": 1752677915041, "results": "187", "hashOfConfig": "161"}, {"size": 9308, "mtime": 1751767716000, "results": "188", "hashOfConfig": "161"}, {"size": 77456, "mtime": 1752409801619, "results": "189", "hashOfConfig": "161"}, {"size": 22490, "mtime": 1752409801616, "results": "190", "hashOfConfig": "161"}, {"size": 26511, "mtime": 1752409801617, "results": "191", "hashOfConfig": "161"}, {"size": 5517, "mtime": 1752409801623, "results": "192", "hashOfConfig": "161"}, {"size": 32410, "mtime": 1752409801620, "results": "193", "hashOfConfig": "161"}, {"size": 42300, "mtime": 1752409801624, "results": "194", "hashOfConfig": "161"}, {"size": 100330, "mtime": 1752409801626, "results": "195", "hashOfConfig": "161"}, {"size": 7690, "mtime": 1751768538000, "results": "196", "hashOfConfig": "161"}, {"size": 15453, "mtime": 1751722792000, "results": "197", "hashOfConfig": "161"}, {"size": 54929, "mtime": 1752409801636, "results": "198", "hashOfConfig": "161"}, {"size": 36510, "mtime": 1752920830735, "results": "199", "hashOfConfig": "161"}, {"size": 3661, "mtime": 1752338196000, "results": "200", "hashOfConfig": "161"}, {"size": 54017, "mtime": 1752829580976, "results": "201", "hashOfConfig": "161"}, {"size": 11659, "mtime": 1752411851791, "results": "202", "hashOfConfig": "161"}, {"size": 1670, "mtime": 1752409801634, "results": "203", "hashOfConfig": "161"}, {"size": 5099, "mtime": 1752836652921, "results": "204", "hashOfConfig": "161"}, {"size": 6863, "mtime": 1752835540150, "results": "205", "hashOfConfig": "161"}, {"size": 5709, "mtime": 1752409801648, "results": "206", "hashOfConfig": "161"}, {"size": 48841, "mtime": 1752409801651, "results": "207", "hashOfConfig": "161"}, {"size": 11615, "mtime": 1752097860000, "results": "208", "hashOfConfig": "161"}, {"size": 27087, "mtime": 1752409801621, "results": "209", "hashOfConfig": "161"}, {"size": 9781, "mtime": 1753216567180, "results": "210", "hashOfConfig": "161"}, {"size": 1734, "mtime": 1751059658000, "results": "211", "hashOfConfig": "161"}, {"size": 2266, "mtime": 1753216737273, "results": "212", "hashOfConfig": "161"}, {"size": 5964, "mtime": 1750646770000, "results": "213", "hashOfConfig": "161"}, {"size": 8117, "mtime": 1752409801668, "results": "214", "hashOfConfig": "161"}, {"size": 10168, "mtime": 1751782620000, "results": "215", "hashOfConfig": "161"}, {"size": 25767, "mtime": 1753270575377, "results": "216", "hashOfConfig": "161"}, {"size": 4076, "mtime": 1751035930000, "results": "217", "hashOfConfig": "161"}, {"size": 4662, "mtime": 1752409801676, "results": "218", "hashOfConfig": "161"}, {"size": 4303, "mtime": 1750620360000, "results": "219", "hashOfConfig": "161"}, {"size": 40890, "mtime": 1752409801601, "results": "220", "hashOfConfig": "161"}, {"size": 49748, "mtime": 1752409801602, "results": "221", "hashOfConfig": "161"}, {"size": 29513, "mtime": 1752409801607, "results": "222", "hashOfConfig": "161"}, {"size": 20882, "mtime": 1752409801608, "results": "223", "hashOfConfig": "161"}, {"size": 7771, "mtime": 1751767656000, "results": "224", "hashOfConfig": "161"}, {"size": 7751, "mtime": 1751767830000, "results": "225", "hashOfConfig": "161"}, {"size": 8583, "mtime": 1751710504000, "results": "226", "hashOfConfig": "161"}, {"size": 9357, "mtime": 1753007074236, "results": "227", "hashOfConfig": "161"}, {"size": 9025, "mtime": 1750975280000, "results": "228", "hashOfConfig": "161"}, {"size": 7922, "mtime": *************, "results": "229", "hashOfConfig": "161"}, {"size": 23471, "mtime": 1752409801655, "results": "230", "hashOfConfig": "161"}, {"size": 5224, "mtime": 1750548386000, "results": "231", "hashOfConfig": "161"}, {"size": 22425, "mtime": 1752677199172, "results": "232", "hashOfConfig": "161"}, {"size": 4066, "mtime": 1750548608000, "results": "233", "hashOfConfig": "161"}, {"size": 3862, "mtime": *************, "results": "234", "hashOfConfig": "161"}, {"size": 5099, "mtime": 1752836952067, "results": "235", "hashOfConfig": "161"}, {"size": 10555, "mtime": 1753260541912, "results": "236", "hashOfConfig": "161"}, {"size": 14135, "mtime": 1752409801671, "results": "237", "hashOfConfig": "161"}, {"size": 6861, "mtime": 1752837015835, "results": "238", "hashOfConfig": "161"}, {"size": 13237, "mtime": 1750509658000, "results": "239", "hashOfConfig": "161"}, {"size": 760, "mtime": 1750582154000, "results": "240", "hashOfConfig": "161"}, {"size": 1464, "mtime": 1753081064033, "results": "241", "hashOfConfig": "161"}, {"size": 61762, "mtime": 1752931709150, "results": "242", "hashOfConfig": "161"}, {"size": 27929, "mtime": 1753099878281, "results": "243", "hashOfConfig": "161"}, {"size": 8754, "mtime": 1752409801579, "results": "244", "hashOfConfig": "161"}, {"size": 0, "mtime": 1751054256000, "results": "245", "hashOfConfig": "161"}, {"size": 21900, "mtime": 1752409801578, "results": "246", "hashOfConfig": "161"}, {"size": 409, "mtime": *************, "results": "247", "hashOfConfig": "161"}, {"size": 10209, "mtime": 1753216677536, "results": "248", "hashOfConfig": "161"}, {"size": 8022, "mtime": 1752409801583, "results": "249", "hashOfConfig": "161"}, {"size": 12095, "mtime": 1752674762201, "results": "250", "hashOfConfig": "161"}, {"size": 12476, "mtime": 1751611456000, "results": "251", "hashOfConfig": "161"}, {"size": 488, "mtime": 1751579418000, "results": "252", "hashOfConfig": "161"}, {"size": 506, "mtime": 1751579400000, "results": "253", "hashOfConfig": "161"}, {"size": 1452, "mtime": 1751437768000, "results": "254", "hashOfConfig": "161"}, {"size": 23613, "mtime": 1752409801630, "results": "255", "hashOfConfig": "161"}, {"size": 10888, "mtime": 1753099907848, "results": "256", "hashOfConfig": "161"}, {"size": 25665, "mtime": 1752409801627, "results": "257", "hashOfConfig": "161"}, {"size": 19281, "mtime": 1752409801594, "results": "258", "hashOfConfig": "161"}, {"size": 16387, "mtime": 1752409801614, "results": "259", "hashOfConfig": "161"}, {"size": 30692, "mtime": 1752409801629, "results": "260", "hashOfConfig": "161"}, {"size": 20468, "mtime": 1752409801591, "results": "261", "hashOfConfig": "161"}, {"size": 23434, "mtime": 1752409801593, "results": "262", "hashOfConfig": "161"}, {"size": 3508, "mtime": 1752049504000, "results": "263", "hashOfConfig": "161"}, {"size": 2614, "mtime": 1751826792000, "results": "264", "hashOfConfig": "161"}, {"size": 1923, "mtime": 1751826502000, "results": "265", "hashOfConfig": "161"}, {"size": 50190, "mtime": 1752409801633, "results": "266", "hashOfConfig": "161"}, {"size": 1927, "mtime": 1751826456000, "results": "267", "hashOfConfig": "161"}, {"size": 23878, "mtime": 1752409801643, "results": "268", "hashOfConfig": "161"}, {"size": 1708, "mtime": 1752408694807, "results": "269", "hashOfConfig": "161"}, {"size": 7643, "mtime": 1750763974000, "results": "270", "hashOfConfig": "161"}, {"size": 23256, "mtime": 1752409801645, "results": "271", "hashOfConfig": "161"}, {"size": 21217, "mtime": 1752409801647, "results": "272", "hashOfConfig": "161"}, {"size": 22643, "mtime": 1752409801644, "results": "273", "hashOfConfig": "161"}, {"size": 2997, "mtime": 1751882506000, "results": "274", "hashOfConfig": "161"}, {"size": 16290, "mtime": 1752409801649, "results": "275", "hashOfConfig": "161"}, {"size": 388, "mtime": 1751774832000, "results": "276", "hashOfConfig": "161"}, {"size": 9439, "mtime": 1751882686000, "results": "277", "hashOfConfig": "161"}, {"size": 8837, "mtime": 1751887002000, "results": "278", "hashOfConfig": "161"}, {"size": 1896, "mtime": 1751609914000, "results": "279", "hashOfConfig": "161"}, {"size": 4484, "mtime": 1751882686000, "results": "280", "hashOfConfig": "161"}, {"size": 4450, "mtime": 1751482138000, "results": "281", "hashOfConfig": "161"}, {"size": 16839, "mtime": 1752683212096, "results": "282", "hashOfConfig": "161"}, {"size": 5743, "mtime": 1750625032000, "results": "283", "hashOfConfig": "161"}, {"size": 5240, "mtime": 1752054098000, "results": "284", "hashOfConfig": "161"}, {"size": 12656, "mtime": 1750906260000, "results": "285", "hashOfConfig": "161"}, {"size": 49712, "mtime": 1751053448000, "results": "286", "hashOfConfig": "161"}, {"size": 7643, "mtime": 1750763974000, "results": "287", "hashOfConfig": "161"}, {"size": 8659, "mtime": 1751780746000, "results": "288", "hashOfConfig": "161"}, {"size": 18218, "mtime": 1752409801667, "results": "289", "hashOfConfig": "161"}, {"size": 9495, "mtime": 1752435437340, "results": "290", "hashOfConfig": "161"}, {"size": 6809, "mtime": 1751608336000, "results": "291", "hashOfConfig": "161"}, {"size": 13304, "mtime": 1752435292539, "results": "292", "hashOfConfig": "161"}, {"size": 5931, "mtime": 1750543146000, "results": "293", "hashOfConfig": "161"}, {"size": 430, "mtime": 1750591742000, "results": "294", "hashOfConfig": "161"}, {"size": 11350, "mtime": 1751037418000, "results": "295", "hashOfConfig": "161"}, {"size": 5807, "mtime": 1750533816000, "results": "296", "hashOfConfig": "161"}, {"size": 7407, "mtime": 1752767163918, "results": "297", "hashOfConfig": "161"}, {"size": 3831, "mtime": *************, "results": "298", "hashOfConfig": "161"}, {"size": 8425, "mtime": 1751813544000, "results": "299", "hashOfConfig": "161"}, {"size": 1406, "mtime": 1750548574000, "results": "300", "hashOfConfig": "161"}, {"size": 18082, "mtime": 1752474860957, "results": "301", "hashOfConfig": "161"}, {"size": 10944, "mtime": 1752484370321, "results": "302", "hashOfConfig": "161"}, {"size": 19638, "mtime": 1753276143523, "results": "303", "hashOfConfig": "161"}, {"size": 17989, "mtime": 1752697222607, "results": "304", "hashOfConfig": "161"}, {"size": 8503, "mtime": 1752680006516, "results": "305", "hashOfConfig": "161"}, {"size": 7673, "mtime": 1752680102177, "results": "306", "hashOfConfig": "161"}, {"size": 626, "mtime": 1751536590000, "results": "307", "hashOfConfig": "161"}, {"size": 18515, "mtime": 1752683186304, "results": "308", "hashOfConfig": "161"}, {"size": 1686, "mtime": 1752409801587, "results": "309", "hashOfConfig": "161"}, {"size": 2272, "mtime": 1752409801638, "results": "310", "hashOfConfig": "161"}, {"size": 6037, "mtime": 1752409801588, "results": "311", "hashOfConfig": "161"}, {"size": 52979, "mtime": 1752685608016, "results": "312", "hashOfConfig": "161"}, {"size": 26870, "mtime": 1752827191316, "results": "313", "hashOfConfig": "161"}, {"size": 16901, "mtime": 1752409801670, "results": "314", "hashOfConfig": "161"}, {"size": 925, "mtime": 1752409801586, "results": "315", "hashOfConfig": "161"}, {"size": 52070, "mtime": 1752697034820, "results": "316", "hashOfConfig": "161"}, {"size": 184, "mtime": 1752749250332, "results": "317", "hashOfConfig": "161"}, {"size": 6386, "mtime": 1753084633902, "results": "318", "hashOfConfig": "161"}, {"size": 8705, "mtime": 1753277683165, "results": "319", "hashOfConfig": "161"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3wgbg", {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js", ["797", "798", "799"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx", ["800"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx", ["801"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx", ["802", "803"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx", ["804", "805"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx", ["806"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx", ["807"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx", ["808", "809"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx", ["810", "811", "812", "813"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx", ["814", "815", "816", "817"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx", ["818", "819", "820"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx", ["821", "822"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx", ["823", "824", "825"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx", ["826", "827"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx", ["828", "829", "830", "831"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx", ["832"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx", ["833", "834"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx", ["835", "836", "837", "838", "839", "840"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx", ["841", "842", "843"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx", ["844", "845", "846", "847"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx", ["848"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx", ["849", "850", "851", "852"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx", ["853"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx", ["854", "855"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx", ["856", "857"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx", ["858", "859", "860", "861", "862", "863"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx", ["864"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx", ["865", "866"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx", ["867"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx", ["868"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx", ["869", "870", "871", "872", "873"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx", ["874", "875"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx", ["876", "877", "878"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx", ["879"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx", ["880", "881"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx", ["882"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx", ["883", "884", "885", "886", "887"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx", ["888", "889", "890", "891", "892", "893", "894", "895", "896", "897", "898", "899", "900", "901", "902", "903", "904"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx", ["905", "906", "907"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx", ["908", "909"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx", ["910"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx", ["911", "912", "913"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx", ["914", "915", "916"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx", ["917", "918", "919"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx", ["920"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx", ["921"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx", ["922", "923"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx", ["924", "925"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx", ["926", "927"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx", ["928"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx", ["929", "930"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx", ["931"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx", ["932"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx", ["933", "934", "935"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx", ["936"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx", ["937", "938", "939", "940", "941", "942"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx", ["943", "944"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx", ["945"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx", ["946", "947"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx", ["948", "949"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx", ["950", "951"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx", ["952"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js", ["953"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx", ["954", "955"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx", ["956"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx", ["957"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx", ["958", "959", "960", "961", "962", "963", "964"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx", ["965"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx", ["966"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx", ["967"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx", ["968"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx", ["969"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx", ["970"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx", ["971", "972", "973", "974"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx", ["975"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx", ["976"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx", ["977", "978", "979", "980", "981", "982", "983"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx", ["984", "985"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx", ["986", "987"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx", ["988"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx", ["989", "990"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx", ["991", "992", "993", "994"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx", ["995"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx", ["996", "997"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx", ["998", "999"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx", ["1000"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx", ["1001", "1002"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx", ["1003", "1004", "1005", "1006"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx", ["1007", "1008"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx", ["1009"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx", ["1010", "1011"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx", ["1012", "1013", "1014", "1015"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx", ["1016", "1017", "1018", "1019", "1020"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx", ["1021"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx", ["1022", "1023"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx", ["1024", "1025", "1026", "1027"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx", ["1028"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx", ["1029"], [], {"ruleId": "1030", "severity": 1, "message": "1031", "line": 1, "column": 25, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 31}, {"ruleId": "1030", "severity": 1, "message": "1034", "line": 1, "column": 33, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 38}, {"ruleId": "1030", "severity": 1, "message": "1035", "line": 1, "column": 40, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 48}, {"ruleId": "1030", "severity": 1, "message": "1036", "line": 3, "column": 17, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1036", "line": 1, "column": 17, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1037", "line": 2, "column": 18, "nodeType": "1032", "messageId": "1033", "endLine": 2, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1038", "line": 5, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1037", "line": 2, "column": 18, "nodeType": "1032", "messageId": "1033", "endLine": 2, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1038", "line": 5, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1039", "line": 7, "column": 20, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 30}, {"ruleId": "1030", "severity": 1, "message": "1040", "line": 14, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 14, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1041", "line": 34, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 34, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1042", "line": 34, "column": 23, "nodeType": "1032", "messageId": "1033", "endLine": 34, "endColumn": 37}, {"ruleId": "1030", "severity": 1, "message": "1043", "line": 24, "column": 25, "nodeType": "1032", "messageId": "1033", "endLine": 24, "endColumn": 41}, {"ruleId": "1030", "severity": 1, "message": "1044", "line": 26, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 26, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1046", "line": 221, "column": 6, "nodeType": "1047", "endLine": 221, "endColumn": 8, "suggestions": "1048"}, {"ruleId": "1049", "severity": 1, "message": "1050", "line": 229, "column": 5, "nodeType": "1051", "messageId": "1052", "endLine": 260, "endColumn": 6}, {"ruleId": "1030", "severity": 1, "message": "1053", "line": 10, "column": 22, "nodeType": "1032", "messageId": "1033", "endLine": 10, "endColumn": 32}, {"ruleId": "1030", "severity": 1, "message": "1054", "line": 49, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 49, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1055", "line": 50, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 50, "endColumn": 24}, {"ruleId": "1045", "severity": 1, "message": "1056", "line": 110, "column": 8, "nodeType": "1047", "endLine": 110, "endColumn": 48, "suggestions": "1057"}, {"ruleId": "1045", "severity": 1, "message": "1058", "line": 94, "column": 6, "nodeType": "1047", "endLine": 94, "endColumn": 26, "suggestions": "1059"}, {"ruleId": "1030", "severity": 1, "message": "1060", "line": 237, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 237, "endColumn": 31}, {"ruleId": "1030", "severity": 1, "message": "1061", "line": 474, "column": 17, "nodeType": "1032", "messageId": "1033", "endLine": 474, "endColumn": 26}, {"ruleId": "1030", "severity": 1, "message": "1062", "line": 212, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 212, "endColumn": 27}, {"ruleId": "1030", "severity": 1, "message": "1063", "line": 229, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 229, "endColumn": 28}, {"ruleId": "1030", "severity": 1, "message": "1064", "line": 13, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 19}, {"ruleId": "1045", "severity": 1, "message": "1065", "line": 131, "column": 8, "nodeType": "1047", "endLine": 131, "endColumn": 67, "suggestions": "1066"}, {"ruleId": "1045", "severity": 1, "message": "1067", "line": 142, "column": 8, "nodeType": "1047", "endLine": 142, "endColumn": 39, "suggestions": "1068"}, {"ruleId": "1030", "severity": 1, "message": "1069", "line": 17, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 17, "endColumn": 17}, {"ruleId": "1045", "severity": 1, "message": "1070", "line": 60, "column": 8, "nodeType": "1047", "endLine": 60, "endColumn": 10, "suggestions": "1071"}, {"ruleId": "1045", "severity": 1, "message": "1072", "line": 79, "column": 6, "nodeType": "1047", "endLine": 79, "endColumn": 82, "suggestions": "1073"}, {"ruleId": "1030", "severity": 1, "message": "1074", "line": 256, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 256, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1074", "line": 278, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 278, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1075", "line": 467, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 467, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1076", "line": 23, "column": 8, "nodeType": "1047", "endLine": 23, "endColumn": 25, "suggestions": "1077"}, {"ruleId": "1030", "severity": 1, "message": "1078", "line": 4, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1079", "line": 20, "column": 6, "nodeType": "1047", "endLine": 20, "endColumn": 44, "suggestions": "1080"}, {"ruleId": "1030", "severity": 1, "message": "1081", "line": 1, "column": 38, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 49}, {"ruleId": "1030", "severity": 1, "message": "1082", "line": 10, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 10, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1083", "line": 52, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 52, "endColumn": 26}, {"ruleId": "1030", "severity": 1, "message": "1084", "line": 267, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 267, "endColumn": 40}, {"ruleId": "1030", "severity": 1, "message": "1085", "line": 704, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 704, "endColumn": 32}, {"ruleId": "1030", "severity": 1, "message": "1086", "line": 740, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 740, "endColumn": 30}, {"ruleId": "1030", "severity": 1, "message": "1064", "line": 19, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 19, "endColumn": 19}, {"ruleId": "1045", "severity": 1, "message": "1087", "line": 163, "column": 8, "nodeType": "1047", "endLine": 163, "endColumn": 26, "suggestions": "1088"}, {"ruleId": "1030", "severity": 1, "message": "1089", "line": 183, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 183, "endColumn": 31}, {"ruleId": "1030", "severity": 1, "message": "1090", "line": 6, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1091", "line": 15, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 17}, {"ruleId": "1045", "severity": 1, "message": "1092", "line": 80, "column": 6, "nodeType": "1047", "endLine": 80, "endColumn": 8, "suggestions": "1093"}, {"ruleId": "1045", "severity": 1, "message": "1092", "line": 88, "column": 6, "nodeType": "1047", "endLine": 88, "endColumn": 14, "suggestions": "1094"}, {"ruleId": "1030", "severity": 1, "message": "1095", "line": 13, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 26}, {"ruleId": "1030", "severity": 1, "message": "1096", "line": 12, "column": 7, "nodeType": "1032", "messageId": "1033", "endLine": 12, "endColumn": 36}, {"ruleId": "1045", "severity": 1, "message": "1097", "line": 109, "column": 8, "nodeType": "1047", "endLine": 109, "endColumn": 10, "suggestions": "1098"}, {"ruleId": "1049", "severity": 1, "message": "1050", "line": 170, "column": 21, "nodeType": "1051", "messageId": "1052", "endLine": 183, "endColumn": 22}, {"ruleId": "1049", "severity": 1, "message": "1050", "line": 477, "column": 9, "nodeType": "1051", "messageId": "1052", "endLine": 556, "endColumn": 10}, {"ruleId": "1045", "severity": 1, "message": "1099", "line": 20, "column": 6, "nodeType": "1047", "endLine": 20, "endColumn": 40, "suggestions": "1100"}, {"ruleId": "1030", "severity": 1, "message": "1101", "line": 5, "column": 22, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 32}, {"ruleId": "1030", "severity": 1, "message": "1102", "line": 45, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 45, "endColumn": 20}, {"ruleId": "1030", "severity": 1, "message": "1103", "line": 5, "column": 98, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 117}, {"ruleId": "1045", "severity": 1, "message": "1104", "line": 351, "column": 6, "nodeType": "1047", "endLine": 351, "endColumn": 20, "suggestions": "1105"}, {"ruleId": "1030", "severity": 1, "message": "1106", "line": 26, "column": 18, "nodeType": "1032", "messageId": "1033", "endLine": 26, "endColumn": 25}, {"ruleId": "1030", "severity": 1, "message": "1107", "line": 27, "column": 19, "nodeType": "1032", "messageId": "1033", "endLine": 27, "endColumn": 27}, {"ruleId": "1030", "severity": 1, "message": "1108", "line": 42, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 42, "endColumn": 27}, {"ruleId": "1045", "severity": 1, "message": "1109", "line": 66, "column": 8, "nodeType": "1047", "endLine": 66, "endColumn": 21, "suggestions": "1110"}, {"ruleId": "1030", "severity": 1, "message": "1111", "line": 268, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 268, "endColumn": 33}, {"ruleId": "1030", "severity": 1, "message": "1112", "line": 282, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 282, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1113", "line": 14, "column": 5, "nodeType": "1032", "messageId": "1033", "endLine": 14, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 9, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 9, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1046", "line": 90, "column": 6, "nodeType": "1047", "endLine": 90, "endColumn": 8, "suggestions": "1115"}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 43, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 43, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 62, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 62, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1117", "line": 25, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 25, "endColumn": 25}, {"ruleId": "1030", "severity": 1, "message": "1118", "line": 25, "column": 27, "nodeType": "1032", "messageId": "1033", "endLine": 25, "endColumn": 45}, {"ruleId": "1030", "severity": 1, "message": "1119", "line": 426, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 426, "endColumn": 25}, {"ruleId": "1030", "severity": 1, "message": "1120", "line": 456, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 456, "endColumn": 29}, {"ruleId": "1045", "severity": 1, "message": "1121", "line": 483, "column": 6, "nodeType": "1047", "endLine": 483, "endColumn": 76, "suggestions": "1122"}, {"ruleId": "1030", "severity": 1, "message": "1123", "line": 40, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 40, "endColumn": 17}, {"ruleId": "1045", "severity": 1, "message": "1124", "line": 80, "column": 6, "nodeType": "1047", "endLine": 80, "endColumn": 17, "suggestions": "1125"}, {"ruleId": "1030", "severity": 1, "message": "1126", "line": 5, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 16}, {"ruleId": "1030", "severity": 1, "message": "1127", "line": 11, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 11, "endColumn": 28}, {"ruleId": "1030", "severity": 1, "message": "1128", "line": 11, "column": 30, "nodeType": "1032", "messageId": "1033", "endLine": 11, "endColumn": 51}, {"ruleId": "1030", "severity": 1, "message": "1129", "line": 5, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 6, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1130", "line": 41, "column": 6, "nodeType": "1047", "endLine": 41, "endColumn": 22, "suggestions": "1131"}, {"ruleId": "1045", "severity": 1, "message": "1132", "line": 15, "column": 6, "nodeType": "1047", "endLine": 15, "endColumn": 20, "suggestions": "1133"}, {"ruleId": "1030", "severity": 1, "message": "1134", "line": 20, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 20, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1135", "line": 20, "column": 26, "nodeType": "1032", "messageId": "1033", "endLine": 20, "endColumn": 43}, {"ruleId": "1045", "severity": 1, "message": "1136", "line": 33, "column": 6, "nodeType": "1047", "endLine": 33, "endColumn": 16, "suggestions": "1137"}, {"ruleId": "1045", "severity": 1, "message": "1138", "line": 44, "column": 6, "nodeType": "1047", "endLine": 44, "endColumn": 18, "suggestions": "1139"}, {"ruleId": "1030", "severity": 1, "message": "1140", "line": 185, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 185, "endColumn": 35}, {"ruleId": "1030", "severity": 1, "message": "1141", "line": 3, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1142", "line": 3, "column": 51, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 61}, {"ruleId": "1030", "severity": 1, "message": "1143", "line": 4, "column": 3, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 12}, {"ruleId": "1030", "severity": 1, "message": "1144", "line": 4, "column": 14, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1145", "line": 4, "column": 54, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 60}, {"ruleId": "1045", "severity": 1, "message": "1146", "line": 38, "column": 6, "nodeType": "1047", "endLine": 38, "endColumn": 16, "suggestions": "1147"}, {"ruleId": "1030", "severity": 1, "message": "1148", "line": 95, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 95, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1149", "line": 128, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 128, "endColumn": 26}, {"ruleId": "1030", "severity": 1, "message": "1150", "line": 171, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 171, "endColumn": 25}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 504, "column": 54, "nodeType": "1153", "messageId": "1154", "endLine": 504, "endColumn": 56}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 504, "column": 143, "nodeType": "1153", "messageId": "1154", "endLine": 504, "endColumn": 145}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 508, "column": 54, "nodeType": "1153", "messageId": "1154", "endLine": 508, "endColumn": 56}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 508, "column": 148, "nodeType": "1153", "messageId": "1154", "endLine": 508, "endColumn": 150}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 512, "column": 54, "nodeType": "1153", "messageId": "1154", "endLine": 512, "endColumn": 56}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 512, "column": 146, "nodeType": "1153", "messageId": "1154", "endLine": 512, "endColumn": 148}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 516, "column": 54, "nodeType": "1153", "messageId": "1154", "endLine": 516, "endColumn": 56}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 516, "column": 144, "nodeType": "1153", "messageId": "1154", "endLine": 516, "endColumn": 146}, {"ruleId": "1030", "severity": 1, "message": "1155", "line": 5, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 25}, {"ruleId": "1030", "severity": 1, "message": "1156", "line": 12, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 12, "endColumn": 18}, {"ruleId": "1045", "severity": 1, "message": "1157", "line": 55, "column": 6, "nodeType": "1047", "endLine": 55, "endColumn": 8, "suggestions": "1158"}, {"ruleId": "1030", "severity": 1, "message": "1159", "line": 1, "column": 27, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 36}, {"ruleId": "1030", "severity": 1, "message": "1160", "line": 3, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 19}, {"ruleId": "1045", "severity": 1, "message": "1161", "line": 42, "column": 6, "nodeType": "1047", "endLine": 42, "endColumn": 29, "suggestions": "1162"}, {"ruleId": "1030", "severity": 1, "message": "1163", "line": 27, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 27, "endColumn": 28}, {"ruleId": "1030", "severity": 1, "message": "1164", "line": 28, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 28, "endColumn": 32}, {"ruleId": "1045", "severity": 1, "message": "1165", "line": 48, "column": 8, "nodeType": "1047", "endLine": 48, "endColumn": 63, "suggestions": "1166"}, {"ruleId": "1030", "severity": 1, "message": "1167", "line": 14, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 14, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1168", "line": 15, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 26}, {"ruleId": "1045", "severity": 1, "message": "1169", "line": 47, "column": 8, "nodeType": "1047", "endLine": 47, "endColumn": 88, "suggestions": "1170"}, {"ruleId": "1030", "severity": 1, "message": "1171", "line": 8, "column": 3, "nodeType": "1032", "messageId": "1033", "endLine": 8, "endColumn": 37}, {"ruleId": "1030", "severity": 1, "message": "1172", "line": 21, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 21, "endColumn": 27}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 217, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 217, "endColumn": 19}, {"ruleId": "1045", "severity": 1, "message": "1173", "line": 26, "column": 6, "nodeType": "1047", "endLine": 26, "endColumn": 48, "suggestions": "1174"}, {"ruleId": "1045", "severity": 1, "message": "1175", "line": 26, "column": 6, "nodeType": "1047", "endLine": 26, "endColumn": 48, "suggestions": "1176"}, {"ruleId": "1030", "severity": 1, "message": "1078", "line": 3, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1177", "line": 27, "column": 6, "nodeType": "1047", "endLine": 27, "endColumn": 62, "suggestions": "1178"}, {"ruleId": "1030", "severity": 1, "message": "1179", "line": 21, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 21, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1180", "line": 88, "column": 6, "nodeType": "1047", "endLine": 88, "endColumn": 40, "suggestions": "1181"}, {"ruleId": "1045", "severity": 1, "message": "1182", "line": 54, "column": 27, "nodeType": "1032", "endLine": 54, "endColumn": 38}, {"ruleId": "1045", "severity": 1, "message": "1183", "line": 86, "column": 6, "nodeType": "1047", "endLine": 86, "endColumn": 12, "suggestions": "1184"}, {"ruleId": "1030", "severity": 1, "message": "1185", "line": 5, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 7, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1186", "line": 107, "column": 8, "nodeType": "1047", "endLine": 107, "endColumn": 39, "suggestions": "1187"}, {"ruleId": "1045", "severity": 1, "message": "1104", "line": 74, "column": 8, "nodeType": "1047", "endLine": 74, "endColumn": 46, "suggestions": "1188"}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 43, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 43, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1189", "line": 5, "column": 57, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 77}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 6, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 14}, {"ruleId": "1045", "severity": 1, "message": "1190", "line": 30, "column": 6, "nodeType": "1047", "endLine": 30, "endColumn": 31, "suggestions": "1191"}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 62, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 62, "endColumn": 19}, {"ruleId": "1045", "severity": 1, "message": "1192", "line": 239, "column": 6, "nodeType": "1047", "endLine": 239, "endColumn": 49, "suggestions": "1193"}, {"ruleId": "1030", "severity": 1, "message": "1194", "line": 658, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 658, "endColumn": 26}, {"ruleId": "1030", "severity": 1, "message": "1195", "line": 709, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 709, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1196", "line": 835, "column": 19, "nodeType": "1032", "messageId": "1033", "endLine": 835, "endColumn": 29}, {"ruleId": "1030", "severity": 1, "message": "1197", "line": 838, "column": 19, "nodeType": "1032", "messageId": "1033", "endLine": 838, "endColumn": 32}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 917, "column": 27, "nodeType": "1200", "endLine": 922, "endColumn": 29}, {"ruleId": "1045", "severity": 1, "message": "1201", "line": 39, "column": 6, "nodeType": "1047", "endLine": 39, "endColumn": 8, "suggestions": "1202"}, {"ruleId": "1045", "severity": 1, "message": "1201", "line": 48, "column": 6, "nodeType": "1047", "endLine": 48, "endColumn": 8, "suggestions": "1203"}, {"ruleId": "1030", "severity": 1, "message": "1204", "line": 584, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 584, "endColumn": 24}, {"ruleId": "1030", "severity": 1, "message": "1205", "line": 12, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 12, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1206", "line": 12, "column": 21, "nodeType": "1032", "messageId": "1033", "endLine": 12, "endColumn": 33}, {"ruleId": "1045", "severity": 1, "message": "1201", "line": 35, "column": 6, "nodeType": "1047", "endLine": 35, "endColumn": 8, "suggestions": "1207"}, {"ruleId": "1045", "severity": 1, "message": "1201", "line": 44, "column": 6, "nodeType": "1047", "endLine": 44, "endColumn": 8, "suggestions": "1208"}, {"ruleId": "1030", "severity": 1, "message": "1205", "line": 15, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1206", "line": 15, "column": 21, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 33}, {"ruleId": "1030", "severity": 1, "message": "1209", "line": 145, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 145, "endColumn": 22}, {"ruleId": "1030", "severity": 1, "message": "1210", "line": 1, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 1, "endColumn": 17}, {"ruleId": "1030", "severity": 1, "message": "1211", "line": 3, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 3, "endColumn": 15}, {"ruleId": "1030", "severity": 1, "message": "1069", "line": 15, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 17}, {"ruleId": "1030", "severity": 1, "message": "1053", "line": 7, "column": 22, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 32}, {"ruleId": "1030", "severity": 1, "message": "1078", "line": 2, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 2, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1212", "line": 6, "column": 39, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 51}, {"ruleId": "1030", "severity": 1, "message": "1213", "line": 13, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1214", "line": 13, "column": 25, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 39}, {"ruleId": "1030", "severity": 1, "message": "1215", "line": 124, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 124, "endColumn": 30}, {"ruleId": "1030", "severity": 1, "message": "1216", "line": 235, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 235, "endColumn": 28}, {"ruleId": "1030", "severity": 1, "message": "1217", "line": 247, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 247, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1218", "line": 255, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 255, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1219", "line": 35, "column": 6, "nodeType": "1047", "endLine": 35, "endColumn": 17, "suggestions": "1220"}, {"ruleId": "1045", "severity": 1, "message": "1221", "line": 45, "column": 6, "nodeType": "1047", "endLine": 45, "endColumn": 17, "suggestions": "1222"}, {"ruleId": "1045", "severity": 1, "message": "1223", "line": 38, "column": 6, "nodeType": "1047", "endLine": 38, "endColumn": 17, "suggestions": "1224"}, {"ruleId": "1045", "severity": 1, "message": "1225", "line": 38, "column": 6, "nodeType": "1047", "endLine": 38, "endColumn": 17, "suggestions": "1226"}, {"ruleId": "1045", "severity": 1, "message": "1227", "line": 35, "column": 6, "nodeType": "1047", "endLine": 35, "endColumn": 17, "suggestions": "1228"}, {"ruleId": "1030", "severity": 1, "message": "1211", "line": 6, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 15}, {"ruleId": "1030", "severity": 1, "message": "1229", "line": 7, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 20}, {"ruleId": "1045", "severity": 1, "message": "1230", "line": 32, "column": 6, "nodeType": "1047", "endLine": 32, "endColumn": 15, "suggestions": "1231"}, {"ruleId": "1030", "severity": 1, "message": "1232", "line": 87, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 87, "endColumn": 21}, {"ruleId": "1030", "severity": 1, "message": "1233", "line": 217, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 217, "endColumn": 26}, {"ruleId": "1045", "severity": 1, "message": "1234", "line": 10, "column": 6, "nodeType": "1047", "endLine": 10, "endColumn": 8, "suggestions": "1235"}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 4, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1212", "line": 6, "column": 39, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 51}, {"ruleId": "1030", "severity": 1, "message": "1213", "line": 13, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1214", "line": 13, "column": 25, "nodeType": "1032", "messageId": "1033", "endLine": 13, "endColumn": 39}, {"ruleId": "1030", "severity": 1, "message": "1215", "line": 124, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 124, "endColumn": 30}, {"ruleId": "1030", "severity": 1, "message": "1216", "line": 235, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 235, "endColumn": 28}, {"ruleId": "1030", "severity": 1, "message": "1217", "line": 247, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 247, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1218", "line": 255, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 255, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1236", "line": 66, "column": 8, "nodeType": "1047", "endLine": 66, "endColumn": 10, "suggestions": "1237"}, {"ruleId": "1045", "severity": 1, "message": "1236", "line": 70, "column": 8, "nodeType": "1047", "endLine": 70, "endColumn": 14, "suggestions": "1238"}, {"ruleId": "1045", "severity": 1, "message": "1239", "line": 33, "column": 6, "nodeType": "1047", "endLine": 33, "endColumn": 21, "suggestions": "1240"}, {"ruleId": "1030", "severity": 1, "message": "1241", "line": 173, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 173, "endColumn": 27}, {"ruleId": "1030", "severity": 1, "message": "1242", "line": 93, "column": 18, "nodeType": "1032", "messageId": "1033", "endLine": 93, "endColumn": 37}, {"ruleId": "1045", "severity": 1, "message": "1236", "line": 21, "column": 6, "nodeType": "1047", "endLine": 21, "endColumn": 8, "suggestions": "1243"}, {"ruleId": "1045", "severity": 1, "message": "1236", "line": 35, "column": 6, "nodeType": "1047", "endLine": 35, "endColumn": 32, "suggestions": "1244"}, {"ruleId": "1045", "severity": 1, "message": "1245", "line": 42, "column": 6, "nodeType": "1047", "endLine": 42, "endColumn": 23, "suggestions": "1246"}, {"ruleId": "1030", "severity": 1, "message": "1247", "line": 75, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 75, "endColumn": 26}, {"ruleId": "1045", "severity": 1, "message": "1248", "line": 163, "column": 6, "nodeType": "1047", "endLine": 163, "endColumn": 33, "suggestions": "1249"}, {"ruleId": "1045", "severity": 1, "message": "1157", "line": 187, "column": 6, "nodeType": "1047", "endLine": 187, "endColumn": 8, "suggestions": "1250"}, {"ruleId": "1251", "severity": 1, "message": "1252", "line": 77, "column": 13, "nodeType": "1200", "endLine": 77, "endColumn": 42}, {"ruleId": "1030", "severity": 1, "message": "1253", "line": 15, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 15, "endColumn": 24}, {"ruleId": "1045", "severity": 1, "message": "1254", "line": 50, "column": 6, "nodeType": "1047", "endLine": 50, "endColumn": 39, "suggestions": "1255"}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 4, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 4, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1256", "line": 30, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 30, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 21, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 21, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1257", "line": 67, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 67, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1116", "line": 80, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 80, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1114", "line": 6, "column": 8, "nodeType": "1032", "messageId": "1033", "endLine": 6, "endColumn": 14}, {"ruleId": "1030", "severity": 1, "message": "1054", "line": 21, "column": 12, "nodeType": "1032", "messageId": "1033", "endLine": 21, "endColumn": 22}, {"ruleId": "1045", "severity": 1, "message": "1072", "line": 39, "column": 8, "nodeType": "1047", "endLine": 39, "endColumn": 85, "suggestions": "1258"}, {"ruleId": "1030", "severity": 1, "message": "1259", "line": 155, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 155, "endColumn": 23}, {"ruleId": "1045", "severity": 1, "message": "1260", "line": 63, "column": 6, "nodeType": "1047", "endLine": 63, "endColumn": 73, "suggestions": "1261"}, {"ruleId": "1251", "severity": 1, "message": "1262", "line": 184, "column": 13, "nodeType": "1200", "endLine": 188, "endColumn": 15}, {"ruleId": "1045", "severity": 1, "message": "1263", "line": 38, "column": 6, "nodeType": "1047", "endLine": 38, "endColumn": 25, "suggestions": "1264"}, {"ruleId": "1030", "severity": 1, "message": "1265", "line": 5, "column": 55, "nodeType": "1032", "messageId": "1033", "endLine": 5, "endColumn": 68}, {"ruleId": "1045", "severity": 1, "message": "1266", "line": 20, "column": 6, "nodeType": "1047", "endLine": 20, "endColumn": 16, "suggestions": "1267"}, {"ruleId": "1030", "severity": 1, "message": "1229", "line": 7, "column": 14, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 24}, {"ruleId": "1045", "severity": 1, "message": "1230", "line": 32, "column": 10, "nodeType": "1047", "endLine": 32, "endColumn": 19, "suggestions": "1268"}, {"ruleId": "1030", "severity": 1, "message": "1232", "line": 87, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 87, "endColumn": 25}, {"ruleId": "1030", "severity": 1, "message": "1233", "line": 217, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 217, "endColumn": 30}, {"ruleId": "1030", "severity": 1, "message": "1269", "line": 56, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 56, "endColumn": 20}, {"ruleId": "1030", "severity": 1, "message": "1270", "line": 89, "column": 25, "nodeType": "1032", "messageId": "1033", "endLine": 89, "endColumn": 41}, {"ruleId": "1030", "severity": 1, "message": "1271", "line": 93, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 93, "endColumn": 26}, {"ruleId": "1045", "severity": 1, "message": "1272", "line": 124, "column": 6, "nodeType": "1047", "endLine": 124, "endColumn": 60, "suggestions": "1273"}, {"ruleId": "1030", "severity": 1, "message": "1274", "line": 278, "column": 9, "nodeType": "1032", "messageId": "1033", "endLine": 278, "endColumn": 33}, {"ruleId": "1030", "severity": 1, "message": "1275", "line": 52, "column": 13, "nodeType": "1032", "messageId": "1033", "endLine": 52, "endColumn": 21}, {"ruleId": "1045", "severity": 1, "message": "1276", "line": 23, "column": 8, "nodeType": "1047", "endLine": 23, "endColumn": 10, "suggestions": "1277"}, {"ruleId": "1030", "severity": 1, "message": "1278", "line": 94, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 94, "endColumn": 23}, {"ruleId": "1030", "severity": 1, "message": "1279", "line": 18, "column": 10, "nodeType": "1032", "messageId": "1033", "endLine": 18, "endColumn": 20}, {"ruleId": "1045", "severity": 1, "message": "1260", "line": 43, "column": 6, "nodeType": "1047", "endLine": 43, "endColumn": 61, "suggestions": "1280"}, {"ruleId": "1030", "severity": 1, "message": "1281", "line": 628, "column": 39, "nodeType": "1032", "messageId": "1033", "endLine": 628, "endColumn": 52}, {"ruleId": "1030", "severity": 1, "message": "1282", "line": 629, "column": 11, "nodeType": "1032", "messageId": "1033", "endLine": 629, "endColumn": 19}, {"ruleId": "1030", "severity": 1, "message": "1039", "line": 7, "column": 32, "nodeType": "1032", "messageId": "1033", "endLine": 7, "endColumn": 42}, {"ruleId": "1045", "severity": 1, "message": "1283", "line": 71, "column": 6, "nodeType": "1047", "endLine": 71, "endColumn": 45, "suggestions": "1284"}, "no-unused-vars", "'Routes' is defined but never used.", "Identifier", "unusedVar", "'Route' is defined but never used.", "'Navigate' is defined but never used.", "'lazy' is defined but never used.", "'Link' is defined but never used.", "'Footer' is defined but never used.", "'sendOTPApi' is defined but never used.", "'organization_id' is assigned a value but never used.", "'isFormValid' is assigned a value but never used.", "'setIsFormValid' is assigned a value but never used.", "'setClassroomData' is assigned a value but never used.", "'dashboardData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["1285"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'decodeData' is defined but never used.", "'totalPages' is assigned a value but never used.", "'totalRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getAllClassroomData'. Either include it or remove the dependency array.", ["1286"], "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1287"], "'handleUpdateAssignment' is assigned a value but never used.", "'dueStatus' is assigned a value but never used.", "'formatDateForInput' is assigned a value but never used.", "'formatToLocalString' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrainees'. Either include it or remove the dependency array.", ["1288"], "React Hook useEffect has a missing dependency: 'fetchAvailableTrainees'. Either include it or remove the dependency array.", ["1289"], "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseList'. Either include it or remove the dependency array.", ["1290"], "React Hook useEffect has a missing dependency: 'fetchLiveClasses'. Either include it or remove the dependency array.", ["1291"], "'response' is assigned a value but never used.", "'formatDateTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseStats'. Either include it or remove the dependency array.", ["1292"], "'Icon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentDetails'. Either include it or remove the dependency array.", ["1293"], "'useCallback' is defined but never used.", "'location' is assigned a value but never used.", "'correctAnswers' is assigned a value but never used.", "'handleOptionContentTypeChange' is assigned a value but never used.", "'renderQuestionContent' is assigned a value but never used.", "'renderOptionContent' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getDocumentDetailsById'. Either include it or remove the dependency array.", ["1294"], "'handleDocumentRemove' is assigned a value but never used.", "'Loader' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseModule'. Either include it or remove the dependency array.", ["1295"], ["1296"], "'decodedCourseId' is assigned a value but never used.", "'REACT_APP_BITMOVIN_PLAYER_KEY' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleContent'. Either include it or remove the dependency array.", ["1297"], "React Hook useEffect has a missing dependency: 'fetchSurveyDetails'. Either include it or remove the dependency array.", ["1298"], "'encodeData' is defined but never used.", "'newVideo' is assigned a value but never used.", "'getQuestionBankById' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1299"], "'setPage' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "'documentPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCertificates'. Either include it or remove the dependency array.", ["1300"], "'handleCreateModalClose' is assigned a value but never used.", "'togglePreview' is assigned a value but never used.", "'sendAnnouncements' is defined but never used.", "'NoData' is defined but never used.", ["1301"], "'formatDate' is assigned a value but never used.", "'showDeleteModal' is assigned a value but never used.", "'setShowDeleteModal' is assigned a value but never used.", "'handleBulkUpload' is assigned a value but never used.", "'handleSampleDownload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTraineesData'. Either include it or remove the dependency array.", ["1302"], "'tabData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1303"], "'TodoList' is defined but never used.", "'monthlyPerformance' is assigned a value but never used.", "'setMonthlyPerformance' is assigned a value but never used.", "'changePassword' is defined but never used.", "React Hook useEffect has missing dependencies: 'courseData?.banner_image', 'courseData?.course_desc', 'courseData?.course_id', 'courseData?.course_name', 'courseData?.course_price', 'courseData?.course_type', 'courseData?.discountCode', 'courseData?.discountValue', and 'courseData?.points'. Either include them or remove the dependency array.", ["1304"], "React Hook useEffect has a missing dependency: 'fetchResults'. Either include it or remove the dependency array.", ["1305"], "'courseProgress' is assigned a value but never used.", "'setCourseProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleList'. Either include it or remove the dependency array.", ["1306"], "React Hook useEffect has a missing dependency: 'selectedContent'. Either include it or remove the dependency array.", ["1307"], "'handleCourseProgressUpdate' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaShareAlt' is defined but never used.", "'FaTwitter' is defined but never used.", "'FaLinkedin' is defined but never used.", "'FaLock' is defined but never used.", "React Hook useEffect has a missing dependency: 'getCourseDetails'. Either include it or remove the dependency array.", ["1308"], "'courseModules' is assigned a value but never used.", "'handleWatchCourse' is assigned a value but never used.", "'hasSubCategories' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'saveRecentVideo' is defined but never used.", "'videoId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initPlayer'. Either include it or remove the dependency array.", ["1309"], "'useEffect' is defined but never used.", "'AccountInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendtovalidation'. Either include it or remove the dependency array.", ["1310"], "'hasMoreQuestions' is assigned a value but never used.", "'availableCurrentPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentQuestions'. Either include it or remove the dependency array.", ["1311"], "'allResults' is assigned a value but never used.", "'groupedResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAnalyticsData' and 'fetchResults'. Either include them or remove the dependency array.", ["1312"], "'gradeClassroomAssignmentSubmission' is defined but never used.", "'assignmentDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentData'. Either include it or remove the dependency array.", ["1313"], "React Hook useEffect has a missing dependency: 'fetchSurveyData'. Either include it or remove the dependency array.", ["1314"], "React Hook useEffect has a missing dependency: 'fetchTraineeProgress'. Either include it or remove the dependency array.", ["1315"], "'classroomName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classroom_id'. Either include it or remove the dependency array.", ["1316"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has missing dependencies: 'fetchClassrooms' and 'search'. Either include them or remove the dependency array.", ["1317"], "'selectedFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleSubmit' and 'timeLeft'. Either include them or remove the dependency array.", ["1318"], ["1319"], "'editOrganisationUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'getTicketData'. Either include it or remove the dependency array.", ["1320"], "React Hook useEffect has a missing dependency: 'currentUserId'. Either include it or remove the dependency array.", ["1321"], "'toggleEmojiPicker' is assigned a value but never used.", "'dislikeMessage' is assigned a value but never used.", "'likesArray' is assigned a value but never used.", "'dislikesArray' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1322"], ["1323"], "'moduleIndex' is assigned a value but never used.", "'collapsed' is assigned a value but never used.", "'setCollapsed' is assigned a value but never used.", ["1324"], ["1325"], "'classroomId' is assigned a value but never used.", "'Cummunity' is defined but never used.", "'toast' is defined but never used.", "'validateDate' is defined but never used.", "'profileData' is assigned a value but never used.", "'setProfileData' is assigned a value but never used.", "'handleCountrySelect' is assigned a value but never used.", "'handleImageChange' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClassroomData'. Either include it or remove the dependency array.", ["1326"], "React Hook useEffect has a missing dependency: 'fetchCourseData'. Either include it or remove the dependency array.", ["1327"], "React Hook useEffect has a missing dependency: 'fetchPaymentData'. Either include it or remove the dependency array.", ["1328"], "React Hook useEffect has a missing dependency: 'fetchTicketsData'. Either include it or remove the dependency array.", ["1329"], "React Hook useEffect has a missing dependency: 'fetchCertificatesData'. Either include it or remove the dependency array.", ["1330"], "'updateLike' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1331"], "'fetchReplies' is assigned a value but never used.", "'handleDeleteReply' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMonthlyPerformance'. Either include it or remove the dependency array.", ["1332"], "React Hook useEffect has a missing dependency: 'searchTerm'. Either include it or remove the dependency array.", ["1333"], ["1334"], "React Hook useEffect has a missing dependency: 'getAssessmentQuestions'. Either include it or remove the dependency array.", ["1335"], "'getScorePercentage' is assigned a value but never used.", "'fetchCourseProgress' is defined but never used.", ["1336"], ["1337"], "React Hook useEffect has a missing dependency: 'contentData'. Either include it or remove the dependency array.", ["1338"], "'saveVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'contentData?.title'. Either include it or remove the dependency array.", ["1339"], ["1340"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'totalQuestions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSurveyQuestions'. Either include it or remove the dependency array.", ["1341"], "'filteredData' is assigned a value but never used.", "'getStatusClass' is assigned a value but never used.", ["1342"], "'clearFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchResources'. Either include it or remove the dependency array.", ["1343"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1344"], "'updateComment' is defined but never used.", "React Hook useEffect has missing dependencies: 'getNotesData' and 'videoId'. Either include them or remove the dependency array.", ["1345"], ["1346"], "'actionType' is assigned a value but never used.", "'setSearchCountry' is assigned a value but never used.", "'filteredCountries' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1347"], "'handleStatusFilterChange' is assigned a value but never used.", "'newState' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["1348"], "'getTaskBadge' is assigned a value but never used.", "'totalCount' is assigned a value but never used.", ["1349"], "'resource_name' is assigned a value but never used.", "'fileName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeZoomMeeting'. Either include it or remove the dependency array.", ["1350"], {"desc": "1351", "fix": "1352"}, {"desc": "1353", "fix": "1354"}, {"desc": "1355", "fix": "1356"}, {"desc": "1357", "fix": "1358"}, {"desc": "1359", "fix": "1360"}, {"desc": "1361", "fix": "1362"}, {"desc": "1363", "fix": "1364"}, {"desc": "1365", "fix": "1366"}, {"desc": "1367", "fix": "1368"}, {"desc": "1369", "fix": "1370"}, {"desc": "1371", "fix": "1372"}, {"desc": "1373", "fix": "1374"}, {"desc": "1375", "fix": "1376"}, {"desc": "1377", "fix": "1378"}, {"desc": "1379", "fix": "1380"}, {"desc": "1381", "fix": "1382"}, {"desc": "1351", "fix": "1383"}, {"desc": "1384", "fix": "1385"}, {"desc": "1386", "fix": "1387"}, {"desc": "1388", "fix": "1389"}, {"desc": "1390", "fix": "1391"}, {"desc": "1392", "fix": "1393"}, {"desc": "1394", "fix": "1395"}, {"desc": "1396", "fix": "1397"}, {"desc": "1398", "fix": "1399"}, {"desc": "1400", "fix": "1401"}, {"desc": "1402", "fix": "1403"}, {"desc": "1404", "fix": "1405"}, {"desc": "1406", "fix": "1407"}, {"desc": "1408", "fix": "1409"}, {"desc": "1410", "fix": "1411"}, {"desc": "1412", "fix": "1413"}, {"desc": "1414", "fix": "1415"}, {"desc": "1416", "fix": "1417"}, {"desc": "1418", "fix": "1419"}, {"desc": "1420", "fix": "1421"}, {"desc": "1422", "fix": "1423"}, {"desc": "1424", "fix": "1425"}, {"desc": "1424", "fix": "1426"}, {"desc": "1424", "fix": "1427"}, {"desc": "1424", "fix": "1428"}, {"desc": "1429", "fix": "1430"}, {"desc": "1431", "fix": "1432"}, {"desc": "1433", "fix": "1434"}, {"desc": "1435", "fix": "1436"}, {"desc": "1437", "fix": "1438"}, {"desc": "1439", "fix": "1440"}, {"desc": "1441", "fix": "1442"}, {"desc": "1443", "fix": "1444"}, {"desc": "1445", "fix": "1446"}, {"desc": "1447", "fix": "1448"}, {"desc": "1443", "fix": "1449"}, {"desc": "1450", "fix": "1451"}, {"desc": "1452", "fix": "1453"}, {"desc": "1454", "fix": "1455"}, {"desc": "1398", "fix": "1456"}, {"desc": "1457", "fix": "1458"}, {"desc": "1459", "fix": "1460"}, {"desc": "1461", "fix": "1462"}, {"desc": "1463", "fix": "1464"}, {"desc": "1465", "fix": "1466"}, {"desc": "1439", "fix": "1467"}, {"desc": "1468", "fix": "1469"}, {"desc": "1470", "fix": "1471"}, {"desc": "1472", "fix": "1473"}, {"desc": "1474", "fix": "1475"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1476", "text": "1477"}, "Update the dependencies array to be: [currentPage, getAllClassroomData, itemsPerPage, searchQuery]", {"range": "1478", "text": "1479"}, "Update the dependencies array to be: [decodedClassroomId, fetchAssignments]", {"range": "1480", "text": "1481"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", {"range": "1482", "text": "1483"}, "Update the dependencies array to be: [fetchAvailableTrainees, modalSearchTerm, showAddModal]", {"range": "1484", "text": "1485"}, "Update the dependencies array to be: [fetchCourseList]", {"range": "1486", "text": "1487"}, "Update the dependencies array to be: [classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1488", "text": "1489"}, "Update the dependencies array to be: [decodedCourseId, fetchCourseStats]", {"range": "1490", "text": "1491"}, "Update the dependencies array to be: [decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", {"range": "1492", "text": "1493"}, "Update the dependencies array to be: [decodedContentId, getDocumentDetailsById]", {"range": "1494", "text": "1495"}, "Update the dependencies array to be: [fetchCourseModule]", {"range": "1496", "text": "1497"}, "Update the dependencies array to be: [fetchCourseModule, search]", {"range": "1498", "text": "1499"}, "Update the dependencies array to be: [fetchModuleContent]", {"range": "1500", "text": "1501"}, "Update the dependencies array to be: [decodedSurveyId, decodedCourseId, fetchSurveyDetails]", {"range": "1502", "text": "1503"}, "Update the dependencies array to be: [fetchQuestions, itemsPerPage]", {"range": "1504", "text": "1505"}, "Update the dependencies array to be: [page, limit, fetchCertificates]", {"range": "1506", "text": "1507"}, {"range": "1508", "text": "1477"}, "Update the dependencies array to be: [pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", {"range": "1509", "text": "1510"}, "Update the dependencies array to be: [decodedId, fetchAnalyticsData]", {"range": "1511", "text": "1512"}, "Update the dependencies array to be: [courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", {"range": "1513", "text": "1514"}, "Update the dependencies array to be: [assessmentId, fetchResults]", {"range": "1515", "text": "1516"}, "Update the dependencies array to be: [courseId, fetchModuleList]", {"range": "1517", "text": "1518"}, "Update the dependencies array to be: [moduleList, selectedContent]", {"range": "1519", "text": "1520"}, "Update the dependencies array to be: [courseId, getCourseDetails]", {"range": "1521", "text": "1522"}, "Update the dependencies array to be: [initPlayer]", {"range": "1523", "text": "1524"}, "Update the dependencies array to be: [course_id, sendtovalidation, session_id]", {"range": "1525", "text": "1526"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", {"range": "1527", "text": "1528"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", {"range": "1529", "text": "1530"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchAssessmentData]", {"range": "1531", "text": "1532"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchSurveyData]", {"range": "1533", "text": "1534"}, "Update the dependencies array to be: [decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", {"range": "1535", "text": "1536"}, "Update the dependencies array to be: [classroom_id, encodedClassroomID, urlActiveTab]", {"range": "1537", "text": "1538"}, "Update the dependencies array to be: [fetchClassrooms, page, search]", {"range": "1539", "text": "1540"}, "Update the dependencies array to be: [handleSubmit, quizStarted, submissionResult, timeLeft]", {"range": "1541", "text": "1542"}, "Update the dependencies array to be: [assignment_id, classroom_id, fetchQuestions, user_id]", {"range": "1543", "text": "1544"}, "Update the dependencies array to be: [page, limit, searchTerm, getTicketData]", {"range": "1545", "text": "1546"}, "Update the dependencies array to be: [classroom_id, currentUserId, group_id, group_name, token]", {"range": "1547", "text": "1548"}, "Update the dependencies array to be: [fetchNotifications]", {"range": "1549", "text": "1550"}, {"range": "1551", "text": "1550"}, {"range": "1552", "text": "1550"}, {"range": "1553", "text": "1550"}, "Update the dependencies array to be: [fetchClassroomData, traineeId]", {"range": "1554", "text": "1555"}, "Update the dependencies array to be: [fetchCourseData, traineeId]", {"range": "1556", "text": "1557"}, "Update the dependencies array to be: [fetchPaymentData, traineeId]", {"range": "1558", "text": "1559"}, "Update the dependencies array to be: [fetchTicketsData, traineeId]", {"range": "1560", "text": "1561"}, "Update the dependencies array to be: [fetchCertificatesData, traineeId]", {"range": "1562", "text": "1563"}, "Update the dependencies array to be: [fetchComments, videoId]", {"range": "1564", "text": "1565"}, "Update the dependencies array to be: [fetchMonthlyPerformance]", {"range": "1566", "text": "1567"}, "Update the dependencies array to be: [searchTerm]", {"range": "1568", "text": "1569"}, "Update the dependencies array to be: [page, searchTerm]", {"range": "1570", "text": "1571"}, "Update the dependencies array to be: [getAssessmentQuestions, moduleData.id]", {"range": "1572", "text": "1573"}, {"range": "1574", "text": "1569"}, "Update the dependencies array to be: [page, hasMore, isLoading, searchTerm]", {"range": "1575", "text": "1576"}, "Update the dependencies array to be: [contentData, contentData?.id]", {"range": "1577", "text": "1578"}, "Update the dependencies array to be: [videoUrl, contentData?.id, contentData?.title]", {"range": "1579", "text": "1580"}, {"range": "1581", "text": "1524"}, "Update the dependencies array to be: [surveyId, moduleData?.completed, fetchSurveyQuestions]", {"range": "1582", "text": "1583"}, "Update the dependencies array to be: [classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1584", "text": "1585"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", {"range": "1586", "text": "1587"}, "Update the dependencies array to be: [videoId, courseId, fetchReviews]", {"range": "1588", "text": "1589"}, "Update the dependencies array to be: [getNotesData, moduleId, videoId]", {"range": "1590", "text": "1591"}, {"range": "1592", "text": "1565"}, "Update the dependencies array to be: [currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", {"range": "1593", "text": "1594"}, "Update the dependencies array to be: [fetchTasks]", {"range": "1595", "text": "1596"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, selectedResourceType, fetchResources]", {"range": "1597", "text": "1598"}, "Update the dependencies array to be: [initializationStarted, loading, error, initializeZoomMeeting]", {"range": "1599", "text": "1600"}, [8379, 8381], "[fetchDashboardData]", [4416, 4456], "[currentPage, getAllClassroomData, itemsPerPage, searchQuery]", [3270, 3290], "[decodedClassroomId, fetchAssignments]", [5315, 5374], "[decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", [5697, 5728], "[fetchAvailableTrainees, modalSearchTerm, showAddModal]", [2474, 2476], "[fetchCourseList]", [2868, 2944], "[classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [935, 952], "[decodedCourseId, fetchCourseStats]", [810, 848], "[decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", [5972, 5990], "[decodedContentId, getDocumentDetailsById]", [3092, 3094], "[fetchCourseModule]", [3269, 3277], "[fetchCourseModule, search]", [4264, 4266], "[fetchModuleContent]", [757, 791], "[decodedSurveyId, decodedCourseId, fetchSurveyDetails]", [10267, 10281], "[fetchQuestions, itemsPerPage]", [2486, 2499], "[page, limit, fetchCertificates]", [3277, 3279], [17596, 17666], "[pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", [2624, 2635], "[decodedId, fetchAnalyticsData]", [1417, 1433], "[courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", [592, 606], "[assessmentId, fetchResults]", [1218, 1228], "[courseId, fetchModuleList]", [1678, 1690], "[module<PERSON>ist, <PERSON><PERSON><PERSON><PERSON>]", [1341, 1351], "[courseId, getCourseDetails]", [1876, 1878], "[initPlayer]", [1258, 1281], "[course_id, sendtovalidation, session_id]", [2503, 2558], "[decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", [2091, 2171], "[decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", [831, 873], "[decodedCourseId, search, pagination.page, fetchAssessmentData]", [806, 848], "[decodedCourseId, search, pagination.page, fetchSurveyData]", [920, 976], "[decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", [3577, 3611], "[classroom_id, encodedClassroomID, urlActiveTab]", [2869, 2875], "[fetchClassrooms, page, search]", [4500, 4531], "[handleSubmit, quizStarted, submissionResult, timeLeft]", [2728, 2766], "[assignment_id, classroom_id, fetchQuestions, user_id]", [1330, 1355], "[page, limit, searchTerm, getTicketData]", [8665, 8708], "[classroom_id, currentUserId, group_id, group_name, token]", [1555, 1557], "[fetchNotifications]", [1813, 1815], [1438, 1440], [1696, 1698], [1209, 1220], "[fetchClassroom<PERSON><PERSON>, traineeId]", [1666, 1677], "[fetchCourseData, traineeId]", [1330, 1341], "[fetchPayment<PERSON><PERSON>, traineeId]", [1325, 1336], "[fetchTickets<PERSON>ata, traineeId]", [1229, 1240], "[fetchCertificates<PERSON><PERSON>, traineeId]", [1367, 1376], "[fetchComments, videoId]", [393, 395], "[fetchMonthlyPerformance]", [2563, 2565], "[searchTerm]", [2657, 2663], "[page, searchTerm]", [1278, 1293], "[getAssessmentQuestions, moduleData.id]", [816, 818], [1229, 1255], "[page, hasMore, isLoading, searchTerm]", [1761, 1778], "[contentData, contentData?.id]", [6036, 6063], "[videoUrl, contentData?.id, contentData?.title]", [6796, 6798], [1804, 1837], "[surveyId, moduleData?.completed, fetchSurveyQuestions]", [1525, 1602], "[classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [2287, 2354], "[decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", [1270, 1289], "[videoId, courseId, fetchReviews]", [673, 683], "[getNotesData, moduleId, videoId]", [1475, 1484], [4256, 4310], "[currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", [824, 826], "[fetchTasks]", [1904, 1959], "[decodedClassroomId, currentPage, selectedResourceType, fetchResources]", [2406, 2445], "[initializationStarted, loading, error, initializeZoomMeeting]"]