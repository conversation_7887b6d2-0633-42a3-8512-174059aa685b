import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Icon } from '@iconify/react';
import { toast } from 'react-toastify';
import logo from '../../assets/images/logo/logo.png';
import { validateEmail, validateForm } from '../../utils/validation';
import { forgotPasswordApi } from '../../services/authService';

function ForgotPassword() {
  const [formData, setFormData] = useState({
    email: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  // Validation Rules
  const validationRules = {
    email: validateEmail
  };

  // Handle Change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Update form data and clear errors
    const newFormData = { ...formData, [name]: value };
    const newErrors = { ...errors, [name]: undefined };

    setFormData(newFormData);
    setErrors(newErrors);
  };

// Handle Submit
const handleSubmit = async (e) => {
  e.preventDefault();

  const validation = validateForm(formData, validationRules);
  if (validation.isValid) {
    setIsLoading(true);

    try {
      console.log("email---------", formData.email);
      console.log("domain---------", window.location.hostname);
      console.log("port---------", window.location.port);
      console.log("full origin---------", window.location.origin); // includes protocol + domain + port

      const payload = {
        email: formData.email,
        domain: window.location.origin
      };

      // Simulate API call with 2 second delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const response = await forgotPasswordApi(payload);
      console.log("response---------", response);

      const otp = response?.data?.otp; // adjust based on actual API r  esponse

      toast.success('OTP sent successfully!', {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        onClose: () => {
          navigate('/auth/forgot-password-otp', {
            state: {
              email: formData.email,
              otp: otp
            }
          });
        }
      });
    } catch (error) {
      console.error('Failed to send OTP:', error);
      toast.error(error.response.data.message);
      setIsLoading(false);
    }
  } else {
    setErrors(validation.errors);
  }
};

  
  return (
    <div className="auth-container">
      <div className="auth-logo">
        <img src={logo} alt="LMS Logo" style={{ width: '180px', height: 'auto' }} />
            </div>
      
      <div className="auth-card">
        <div className="auth-header">
          <h2>Forgot Password</h2>
          <p>Enter your email to reset password</p>
                </div>
        
        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-group">
            <label htmlFor="email">Email Address</label>
            <div className="auth-input-wrapper">
              <Icon icon="mdi:email-outline" className="auth-input-icon" />
              <input 
                type="text" 
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email" 
                className={`auth-input ${errors.email ? 'error' : ''}`}
                disabled={isLoading}
              />
            </div>
            {errors.email && <span className="auth-error-message">{errors.email[0]}</span>}
          </div>

          <button 
            type="submit" 
            className={`btn btn-primary ${isLoading ? 'loading' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="spinner"></div>
                Sending Reset Link...
              </>
            ) : (
              'Send Reset Link'
            )}
          </button>
          
          <div className="auth-options">
            Remember your password? 
            <Link 
              to="/auth/login" 
              className={isLoading ? 'disabled-link' : ''}
            >
              &nbsp; Login here
            </Link>
        </div>
        </form>
      </div>
    </div>
  );
}

export default ForgotPassword;