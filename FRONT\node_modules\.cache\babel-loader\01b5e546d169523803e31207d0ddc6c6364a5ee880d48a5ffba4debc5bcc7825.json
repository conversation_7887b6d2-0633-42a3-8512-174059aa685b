{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\components\\\\zoom\\\\ZoomMeeting.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { endZoomLiveClass } from '../../services/adminService';\n\n// Zoom SDK Error Boundary\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ZoomErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError() {\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, info) {\n    console.log('🔵 Zoom SDK Error caught by boundary:', error, info);\n  }\n  render() {\n    if (this.state.hasError) return null;\n    return this.props.children;\n  }\n}\nconst ZoomMeeting = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [initializationStarted, setInitializationStarted] = useState(false);\n  const meetingNumber = searchParams.get('meetingNumber');\n  const role = searchParams.get('role');\n  const signature = searchParams.get('signature');\n  const sdkKey = searchParams.get('sdkKey');\n  const userName = searchParams.get('userName') || (role === '1' ? 'Host' : 'Attendee');\n  const meetingPassword = searchParams.get('password') || '';\n  const liveClassId = searchParams.get('liveClassId'); // Get live class ID for API calls\n\n  useEffect(() => {\n    if (!meetingNumber || !signature || !sdkKey || role === null) {\n      setError('Missing required meeting parameters');\n      setLoading(false);\n      return;\n    }\n    fetch('https://source.zoom.us/ping', {\n      mode: 'no-cors'\n    }).catch(() => {});\n    return () => {\n      // Clear Zoom-rendered DOM on cleanup\n      try {\n        const zoomRoot = document.getElementById('zoom-root');\n        if (zoomRoot) zoomRoot.innerHTML = '';\n      } catch (e) {\n        console.warn('Zoom cleanup error:', e);\n      }\n    };\n  }, [meetingNumber, signature, sdkKey, role]);\n  useEffect(() => {\n    if (!initializationStarted && !error) {\n      setInitializationStarted(true);\n      const timeoutId = setTimeout(() => {\n        if (loading) {\n          setError('Meeting initialization timed out. Please try again.');\n          setLoading(false);\n        }\n      }, 60000);\n      initializeZoomMeeting().finally(() => clearTimeout(timeoutId));\n    }\n  }, [initializationStarted, loading, error]);\n  const loadZoomSDK = async () => {\n    if (window.ZoomMtg) return window.ZoomMtg;\n    const scripts = ['https://source.zoom.us/4.0.0/lib/vendor/react.min.js', 'https://source.zoom.us/4.0.0/lib/vendor/react-dom.min.js', 'https://source.zoom.us/4.0.0/lib/vendor/redux.min.js', 'https://source.zoom.us/4.0.0/lib/vendor/redux-thunk.min.js', 'https://source.zoom.us/4.0.0/zoom-meeting-4.0.0.min.js'];\n    await Promise.all(scripts.map(src => new Promise((res, rej) => {\n      if (document.querySelector(`script[src=\"${src}\"]`)) return res();\n      const s = document.createElement('script');\n      s.src = src;\n      s.async = false;\n      s.onload = res;\n      s.onerror = () => rej(new Error(`Failed to load: ${src}`));\n      document.head.appendChild(s);\n    })));\n    return new Promise((resolve, reject) => {\n      let attempts = 0;\n      (function check() {\n        if (window.ZoomMtg) return resolve(window.ZoomMtg);\n        if (++attempts > 20) return reject(new Error('ZoomMtg not available'));\n        setTimeout(check, 100);\n      })();\n    });\n  };\n  const initializeZoomMeeting = async () => {\n    try {\n      const ZoomMtg = await loadZoomSDK();\n      ZoomMtg.setZoomJSLib('https://source.zoom.us/4.0.0/lib', '/av');\n      ZoomMtg.preLoadWasm();\n      ZoomMtg.prepareWebSDK();\n      ZoomMtg.i18n.load('en-US');\n      ZoomMtg.i18n.onLoad(() => {\n        // Determine redirect URL based on user role\n        const redirectUrl = role === '1' ? `${window.location.origin}/admin/classrooms` : `${window.location.origin}/user/AllClassroom`;\n        console.log('Setting Zoom leaveUrl based on role:', role, '-> redirectUrl:', redirectUrl);\n        ZoomMtg.init({\n          leaveUrl: redirectUrl,\n          disableCORP: !window.crossOriginIsolated,\n          // Tell SDK which DOM node to control\n          success: () => joinMeeting(ZoomMtg),\n          error: e => {\n            setError('Failed to initialize Zoom SDK: ' + (e.errorMessage || e.message));\n            setLoading(false);\n          }\n        });\n      });\n    } catch (err) {\n      setError('Zoom SDK init error: ' + err.message);\n      setLoading(false);\n    }\n  };\n  const joinMeeting = ZoomMtg => {\n    let userLeftMeeting = false;\n\n    // Listen for when user leaves meeting (this fires when \"Leave Meeting\" is clicked)\n    ZoomMtg.inMeetingServiceListener('onUserLeave', data => {\n      console.log('User leave event:', data);\n      userLeftMeeting = true;\n      console.log('User left meeting (meeting continues for others)');\n      // The leaveUrl will handle the redirect automatically\n    });\n\n    // Listen for when meeting is ended by host (this fires when \"End Meeting for All\" is clicked)\n    ZoomMtg.inMeetingServiceListener('onMeetingEnded', data => {\n      console.log('Meeting ended by host event:', data);\n      if (!userLeftMeeting && role === '1') {\n        console.log('Host ended meeting for all participants');\n        handleMeetingEnd();\n      }\n    });\n\n    // Additional listener for meeting disconnection\n    ZoomMtg.inMeetingServiceListener('onMeetingDisconnected', data => {\n      console.log('Meeting disconnected event:', data);\n      if (!userLeftMeeting && role === '1') {\n        console.log('Meeting disconnected - calling API');\n        handleMeetingEnd();\n      }\n    });\n\n    // Add event listeners for meeting end events\n    ZoomMtg.inMeetingServiceListener('onMeetingStatus', data => {\n      console.log('Meeting status changed:', data);\n\n      // Only call API if user didn't just leave the meeting AND is admin\n      if (data.meetingStatus === 3 && !userLeftMeeting && role === '1') {\n        console.log('Meeting ended for all participants detected via Zoom SDK');\n        handleMeetingEnd();\n      } else if (data.meetingStatus === 3 && userLeftMeeting) {\n        console.log('Meeting status changed due to user leaving - not calling API');\n      }\n    });\n    ZoomMtg.join({\n      meetingNumber: meetingNumber.replace(/\\D/g, ''),\n      signature,\n      userName,\n      userEmail: '',\n      passWord: meetingPassword,\n      tk: '',\n      success: () => {\n        setLoading(false);\n        toast.success('Successfully joined the meeting!');\n      },\n      error: e => {\n        setError('Failed to join meeting: ' + (e.errorMessage || 'Unknown error'));\n        setLoading(false);\n      }\n    });\n  };\n  const handleGoBack = () => {\n    const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n    navigate(redirectPath);\n  };\n\n  // Function to handle meeting end via API\n  const handleMeetingEnd = async () => {\n    if (!liveClassId) {\n      console.warn('No live class ID available for ending meeting');\n      return;\n    }\n    try {\n      console.log('Meeting ended via Zoom controls, calling API to update database...');\n      const response = await endZoomLiveClass({\n        live_class_id: liveClassId\n      });\n      console.log('Meeting end API response:', response);\n      toast.success('Meeting ended successfully!');\n\n      // Redirect to appropriate page after successful API call\n      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n      setTimeout(() => {\n        navigate(redirectPath);\n      }, 1000); // Small delay to show the success message\n    } catch (error) {\n      console.error('Error ending meeting via API:', error);\n      // Don't show error toast as meeting is already ended on Zoom side\n      // Still redirect to appropriate page even if API call fails\n      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n      setTimeout(() => {\n        navigate(redirectPath);\n      }, 1000);\n    }\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-fluid vh-100 d-flex align-items-center justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle text-danger mb-4\",\n          style: {\n            fontSize: '48px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-danger mb-3\",\n          children: \"Meeting Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-4\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: handleGoBack,\n          children: \"Go Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this);\n  }\n\n  // React does not render anything; Zoom lives in #zoom-root\n  return null;\n};\n_s(ZoomMeeting, \"eWI4fqIfzWPtXKVJZ0NW4lSzCV4=\", false, function () {\n  return [useSearchParams, useNavigate];\n});\n_c = ZoomMeeting;\nexport default function ZoomMeetingWithErrorBoundary() {\n  return /*#__PURE__*/_jsxDEV(ZoomErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ZoomMeeting, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n}\n_c2 = ZoomMeetingWithErrorBoundary;\nvar _c, _c2;\n$RefreshReg$(_c, \"ZoomMeeting\");\n$RefreshReg$(_c2, \"ZoomMeetingWithErrorBoundary\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSearchParams", "useNavigate", "toast", "endZoomLiveClass", "jsxDEV", "_jsxDEV", "ZoomErrorBoundary", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "error", "info", "console", "log", "render", "children", "ZoomMeeting", "_s", "searchParams", "navigate", "loading", "setLoading", "setError", "initializationStarted", "setInitializationStarted", "meetingNumber", "get", "role", "signature", "sdkKey", "userName", "meetingPassword", "liveClassId", "fetch", "mode", "catch", "zoomRoot", "document", "getElementById", "innerHTML", "e", "warn", "timeoutId", "setTimeout", "initializeZoomMeeting", "finally", "clearTimeout", "loadZoomSDK", "window", "ZoomMtg", "scripts", "Promise", "all", "map", "src", "res", "rej", "querySelector", "s", "createElement", "async", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "resolve", "reject", "attempts", "check", "setZoomJSLib", "preLoadWasm", "prepareWebSDK", "i18n", "load", "onLoad", "redirectUrl", "location", "origin", "init", "leaveUrl", "disableCORP", "crossOriginIsolated", "success", "joinMeeting", "errorMessage", "message", "err", "userLeftMeeting", "inMeetingServiceListener", "data", "handleMeetingEnd", "meetingStatus", "join", "replace", "userEmail", "passWord", "tk", "handleGoBack", "redirectPath", "response", "live_class_id", "className", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "ZoomMeetingWithErrorBoundary", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/components/zoom/ZoomMeeting.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { endZoomLiveClass } from '../../services/adminService';\n\n// Zoom SDK Error Boundary\nclass ZoomErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n  static getDerivedStateFromError() { return { hasError: true }; }\n  componentDidCatch(error, info) {\n    console.log('🔵 Zoom SDK Error caught by boundary:', error, info);\n  }\n  render() {\n    if (this.state.hasError) return null;\n    return this.props.children;\n  }\n}\n\nconst ZoomMeeting = () => {\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [initializationStarted, setInitializationStarted] = useState(false);\n\n  const meetingNumber = searchParams.get('meetingNumber');\n  const role = searchParams.get('role');\n  const signature = searchParams.get('signature');\n  const sdkKey = searchParams.get('sdkKey');\n  const userName = searchParams.get('userName') || (role === '1' ? 'Host' : 'Attendee');\n  const meetingPassword = searchParams.get('password') || '';\n  const liveClassId = searchParams.get('liveClassId'); // Get live class ID for API calls\n\n  useEffect(() => {\n    if (!meetingNumber || !signature || !sdkKey || role === null) {\n      setError('Missing required meeting parameters');\n      setLoading(false);\n      return;\n    }\n\n    fetch('https://source.zoom.us/ping', { mode: 'no-cors' }).catch(() => {});\n\n    return () => {\n      // Clear Zoom-rendered DOM on cleanup\n      try {\n        const zoomRoot = document.getElementById('zoom-root');\n        if (zoomRoot) zoomRoot.innerHTML = '';\n      } catch (e) {\n        console.warn('Zoom cleanup error:', e);\n      }\n    };\n  }, [meetingNumber, signature, sdkKey, role]);\n\n  useEffect(() => {\n    if (!initializationStarted && !error) {\n      setInitializationStarted(true);\n\n      const timeoutId = setTimeout(() => {\n        if (loading) {\n          setError('Meeting initialization timed out. Please try again.');\n          setLoading(false);\n        }\n      }, 60000);\n\n      initializeZoomMeeting().finally(() => clearTimeout(timeoutId));\n    }\n  }, [initializationStarted, loading, error]);\n\n  const loadZoomSDK = async () => {\n    if (window.ZoomMtg) return window.ZoomMtg;\n\n    const scripts = [\n      'https://source.zoom.us/4.0.0/lib/vendor/react.min.js',\n      'https://source.zoom.us/4.0.0/lib/vendor/react-dom.min.js',\n      'https://source.zoom.us/4.0.0/lib/vendor/redux.min.js',\n      'https://source.zoom.us/4.0.0/lib/vendor/redux-thunk.min.js',\n      'https://source.zoom.us/4.0.0/zoom-meeting-4.0.0.min.js'\n    ];\n\n    await Promise.all(scripts.map(src => new Promise((res, rej) => {\n      if (document.querySelector(`script[src=\"${src}\"]`)) return res();\n      const s = document.createElement('script');\n      s.src = src;\n      s.async = false;\n      s.onload = res;\n      s.onerror = () => rej(new Error(`Failed to load: ${src}`));\n      document.head.appendChild(s);\n    })));\n\n    return new Promise((resolve, reject) => {\n      let attempts = 0;\n      (function check() {\n        if (window.ZoomMtg) return resolve(window.ZoomMtg);\n        if (++attempts > 20) return reject(new Error('ZoomMtg not available'));\n        setTimeout(check, 100);\n      })();\n    });\n  };\n\n  const initializeZoomMeeting = async () => {\n    try {\n      const ZoomMtg = await loadZoomSDK();\n\n      ZoomMtg.setZoomJSLib('https://source.zoom.us/4.0.0/lib', '/av');\n      ZoomMtg.preLoadWasm();\n      ZoomMtg.prepareWebSDK();\n\n      ZoomMtg.i18n.load('en-US');\n      ZoomMtg.i18n.onLoad(() => {\n        // Determine redirect URL based on user role\n        const redirectUrl = role === '1'\n          ? `${window.location.origin}/admin/classrooms`\n          : `${window.location.origin}/user/AllClassroom`;\n\n        console.log('Setting Zoom leaveUrl based on role:', role, '-> redirectUrl:', redirectUrl);\n\n        ZoomMtg.init({\n          leaveUrl: redirectUrl,\n          disableCORP: !window.crossOriginIsolated,\n          // Tell SDK which DOM node to control\n          success: () => joinMeeting(ZoomMtg),\n          error: (e) => {\n            setError('Failed to initialize Zoom SDK: ' + (e.errorMessage || e.message));\n            setLoading(false);\n          }\n        });\n      });\n    } catch (err) {\n      setError('Zoom SDK init error: ' + err.message);\n      setLoading(false);\n    }\n  };\n\n  const joinMeeting = (ZoomMtg) => {\n    let userLeftMeeting = false;\n\n    // Listen for when user leaves meeting (this fires when \"Leave Meeting\" is clicked)\n    ZoomMtg.inMeetingServiceListener('onUserLeave', (data) => {\n      console.log('User leave event:', data);\n      userLeftMeeting = true;\n      console.log('User left meeting (meeting continues for others)');\n      // The leaveUrl will handle the redirect automatically\n    });\n\n    // Listen for when meeting is ended by host (this fires when \"End Meeting for All\" is clicked)\n    ZoomMtg.inMeetingServiceListener('onMeetingEnded', (data) => {\n      console.log('Meeting ended by host event:', data);\n      if (!userLeftMeeting && role === '1') {\n        console.log('Host ended meeting for all participants');\n        handleMeetingEnd();\n      }\n    });\n\n    // Additional listener for meeting disconnection\n    ZoomMtg.inMeetingServiceListener('onMeetingDisconnected', (data) => {\n      console.log('Meeting disconnected event:', data);\n      if (!userLeftMeeting && role === '1') {\n        console.log('Meeting disconnected - calling API');\n        handleMeetingEnd();\n      }\n    });\n\n    // Add event listeners for meeting end events\n    ZoomMtg.inMeetingServiceListener('onMeetingStatus', (data) => {\n      console.log('Meeting status changed:', data);\n\n      // Only call API if user didn't just leave the meeting AND is admin\n      if (data.meetingStatus === 3 && !userLeftMeeting && role === '1') {\n        console.log('Meeting ended for all participants detected via Zoom SDK');\n        handleMeetingEnd();\n      } else if (data.meetingStatus === 3 && userLeftMeeting) {\n        console.log('Meeting status changed due to user leaving - not calling API');\n      }\n    });\n\n    ZoomMtg.join({\n      meetingNumber: meetingNumber.replace(/\\D/g, ''),\n      signature,\n      userName,\n      userEmail: '',\n      passWord: meetingPassword,\n      tk: '',\n      success: () => {\n        setLoading(false);\n        toast.success('Successfully joined the meeting!');\n      },\n      error: (e) => {\n        setError('Failed to join meeting: ' + (e.errorMessage || 'Unknown error'));\n        setLoading(false);\n      }\n    });\n  };\n\n  const handleGoBack = () => {\n    const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n    navigate(redirectPath);\n  };\n\n  // Function to handle meeting end via API\n  const handleMeetingEnd = async () => {\n    if (!liveClassId) {\n      console.warn('No live class ID available for ending meeting');\n      return;\n    }\n\n    try {\n      console.log('Meeting ended via Zoom controls, calling API to update database...');\n      const response = await endZoomLiveClass({ live_class_id: liveClassId });\n      console.log('Meeting end API response:', response);\n      toast.success('Meeting ended successfully!');\n\n      // Redirect to appropriate page after successful API call\n      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n      setTimeout(() => {\n        navigate(redirectPath);\n      }, 1000); // Small delay to show the success message\n\n    } catch (error) {\n      console.error('Error ending meeting via API:', error);\n      // Don't show error toast as meeting is already ended on Zoom side\n      // Still redirect to appropriate page even if API call fails\n      const redirectPath = role === '1' ? '/admin/classrooms' : '/user/AllClassroom';\n      setTimeout(() => {\n        navigate(redirectPath);\n      }, 1000);\n    }\n  };\n\n  if (error) {\n    return (\n      <div className=\"container-fluid vh-100 d-flex align-items-center justify-content-center\">\n        <div className=\"text-center\">\n          <i className=\"fas fa-exclamation-triangle text-danger mb-4\" style={{ fontSize: '48px' }} />\n          <h4 className=\"text-danger mb-3\">Meeting Error</h4>\n          <p className=\"text-muted mb-4\">{error}</p>\n          <button className=\"btn btn-primary\" onClick={handleGoBack}>Go Back</button>\n        </div>\n      </div>\n    );\n  }\n\n  // React does not render anything; Zoom lives in #zoom-root\n  return null;\n};\n\nexport default function ZoomMeetingWithErrorBoundary() {\n  return (\n    <ZoomErrorBoundary>\n      <ZoomMeeting />\n    </ZoomErrorBoundary>\n  );\n}\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,eAAe,EAAEC,WAAW,QAAQ,kBAAkB;AAC/D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,gBAAgB,QAAQ,6BAA6B;;AAE9D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,SAAST,KAAK,CAACU,SAAS,CAAC;EAC9CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;EAClC;EACA,OAAOC,wBAAwBA,CAAA,EAAG;IAAE,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAAE;EAC/DE,iBAAiBA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC7BC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEH,KAAK,EAAEC,IAAI,CAAC;EACnE;EACAG,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE,OAAO,IAAI;IACpC,OAAO,IAAI,CAACF,KAAK,CAACU,QAAQ;EAC5B;AACF;AAEA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,YAAY,CAAC,GAAGtB,eAAe,CAAC,CAAC;EACxC,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEY,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAM8B,aAAa,GAAGP,YAAY,CAACQ,GAAG,CAAC,eAAe,CAAC;EACvD,MAAMC,IAAI,GAAGT,YAAY,CAACQ,GAAG,CAAC,MAAM,CAAC;EACrC,MAAME,SAAS,GAAGV,YAAY,CAACQ,GAAG,CAAC,WAAW,CAAC;EAC/C,MAAMG,MAAM,GAAGX,YAAY,CAACQ,GAAG,CAAC,QAAQ,CAAC;EACzC,MAAMI,QAAQ,GAAGZ,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,KAAKC,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,UAAU,CAAC;EACrF,MAAMI,eAAe,GAAGb,YAAY,CAACQ,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;EAC1D,MAAMM,WAAW,GAAGd,YAAY,CAACQ,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;;EAErDhC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,aAAa,IAAI,CAACG,SAAS,IAAI,CAACC,MAAM,IAAIF,IAAI,KAAK,IAAI,EAAE;MAC5DL,QAAQ,CAAC,qCAAqC,CAAC;MAC/CD,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAY,KAAK,CAAC,6BAA6B,EAAE;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEzE,OAAO,MAAM;MACX;MACA,IAAI;QACF,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,WAAW,CAAC;QACrD,IAAIF,QAAQ,EAAEA,QAAQ,CAACG,SAAS,GAAG,EAAE;MACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV5B,OAAO,CAAC6B,IAAI,CAAC,qBAAqB,EAAED,CAAC,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACf,aAAa,EAAEG,SAAS,EAAEC,MAAM,EAAEF,IAAI,CAAC,CAAC;EAE5CjC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6B,qBAAqB,IAAI,CAACb,KAAK,EAAE;MACpCc,wBAAwB,CAAC,IAAI,CAAC;MAE9B,MAAMkB,SAAS,GAAGC,UAAU,CAAC,MAAM;QACjC,IAAIvB,OAAO,EAAE;UACXE,QAAQ,CAAC,qDAAqD,CAAC;UAC/DD,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,EAAE,KAAK,CAAC;MAETuB,qBAAqB,CAAC,CAAC,CAACC,OAAO,CAAC,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;IAChE;EACF,CAAC,EAAE,CAACnB,qBAAqB,EAAEH,OAAO,EAAEV,KAAK,CAAC,CAAC;EAE3C,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAIC,MAAM,CAACC,OAAO,EAAE,OAAOD,MAAM,CAACC,OAAO;IAEzC,MAAMC,OAAO,GAAG,CACd,sDAAsD,EACtD,0DAA0D,EAC1D,sDAAsD,EACtD,4DAA4D,EAC5D,wDAAwD,CACzD;IAED,MAAMC,OAAO,CAACC,GAAG,CAACF,OAAO,CAACG,GAAG,CAACC,GAAG,IAAI,IAAIH,OAAO,CAAC,CAACI,GAAG,EAAEC,GAAG,KAAK;MAC7D,IAAInB,QAAQ,CAACoB,aAAa,CAAC,eAAeH,GAAG,IAAI,CAAC,EAAE,OAAOC,GAAG,CAAC,CAAC;MAChE,MAAMG,CAAC,GAAGrB,QAAQ,CAACsB,aAAa,CAAC,QAAQ,CAAC;MAC1CD,CAAC,CAACJ,GAAG,GAAGA,GAAG;MACXI,CAAC,CAACE,KAAK,GAAG,KAAK;MACfF,CAAC,CAACG,MAAM,GAAGN,GAAG;MACdG,CAAC,CAACI,OAAO,GAAG,MAAMN,GAAG,CAAC,IAAIO,KAAK,CAAC,mBAAmBT,GAAG,EAAE,CAAC,CAAC;MAC1DjB,QAAQ,CAAC2B,IAAI,CAACC,WAAW,CAACP,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC;IAEJ,OAAO,IAAIP,OAAO,CAAC,CAACe,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAIC,QAAQ,GAAG,CAAC;MAChB,CAAC,SAASC,KAAKA,CAAA,EAAG;QAChB,IAAIrB,MAAM,CAACC,OAAO,EAAE,OAAOiB,OAAO,CAAClB,MAAM,CAACC,OAAO,CAAC;QAClD,IAAI,EAAEmB,QAAQ,GAAG,EAAE,EAAE,OAAOD,MAAM,CAAC,IAAIJ,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACtEpB,UAAU,CAAC0B,KAAK,EAAE,GAAG,CAAC;MACxB,CAAC,EAAE,CAAC;IACN,CAAC,CAAC;EACJ,CAAC;EAED,MAAMzB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMK,OAAO,GAAG,MAAMF,WAAW,CAAC,CAAC;MAEnCE,OAAO,CAACqB,YAAY,CAAC,kCAAkC,EAAE,KAAK,CAAC;MAC/DrB,OAAO,CAACsB,WAAW,CAAC,CAAC;MACrBtB,OAAO,CAACuB,aAAa,CAAC,CAAC;MAEvBvB,OAAO,CAACwB,IAAI,CAACC,IAAI,CAAC,OAAO,CAAC;MAC1BzB,OAAO,CAACwB,IAAI,CAACE,MAAM,CAAC,MAAM;QACxB;QACA,MAAMC,WAAW,GAAGjD,IAAI,KAAK,GAAG,GAC5B,GAAGqB,MAAM,CAAC6B,QAAQ,CAACC,MAAM,mBAAmB,GAC5C,GAAG9B,MAAM,CAAC6B,QAAQ,CAACC,MAAM,oBAAoB;QAEjDlE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEc,IAAI,EAAE,iBAAiB,EAAEiD,WAAW,CAAC;QAEzF3B,OAAO,CAAC8B,IAAI,CAAC;UACXC,QAAQ,EAAEJ,WAAW;UACrBK,WAAW,EAAE,CAACjC,MAAM,CAACkC,mBAAmB;UACxC;UACAC,OAAO,EAAEA,CAAA,KAAMC,WAAW,CAACnC,OAAO,CAAC;UACnCvC,KAAK,EAAG8B,CAAC,IAAK;YACZlB,QAAQ,CAAC,iCAAiC,IAAIkB,CAAC,CAAC6C,YAAY,IAAI7C,CAAC,CAAC8C,OAAO,CAAC,CAAC;YAC3EjE,UAAU,CAAC,KAAK,CAAC;UACnB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOkE,GAAG,EAAE;MACZjE,QAAQ,CAAC,uBAAuB,GAAGiE,GAAG,CAACD,OAAO,CAAC;MAC/CjE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,WAAW,GAAInC,OAAO,IAAK;IAC/B,IAAIuC,eAAe,GAAG,KAAK;;IAE3B;IACAvC,OAAO,CAACwC,wBAAwB,CAAC,aAAa,EAAGC,IAAI,IAAK;MACxD9E,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE6E,IAAI,CAAC;MACtCF,eAAe,GAAG,IAAI;MACtB5E,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D;IACF,CAAC,CAAC;;IAEF;IACAoC,OAAO,CAACwC,wBAAwB,CAAC,gBAAgB,EAAGC,IAAI,IAAK;MAC3D9E,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE6E,IAAI,CAAC;MACjD,IAAI,CAACF,eAAe,IAAI7D,IAAI,KAAK,GAAG,EAAE;QACpCf,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtD8E,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA1C,OAAO,CAACwC,wBAAwB,CAAC,uBAAuB,EAAGC,IAAI,IAAK;MAClE9E,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE6E,IAAI,CAAC;MAChD,IAAI,CAACF,eAAe,IAAI7D,IAAI,KAAK,GAAG,EAAE;QACpCf,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD8E,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA1C,OAAO,CAACwC,wBAAwB,CAAC,iBAAiB,EAAGC,IAAI,IAAK;MAC5D9E,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6E,IAAI,CAAC;;MAE5C;MACA,IAAIA,IAAI,CAACE,aAAa,KAAK,CAAC,IAAI,CAACJ,eAAe,IAAI7D,IAAI,KAAK,GAAG,EAAE;QAChEf,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE8E,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAID,IAAI,CAACE,aAAa,KAAK,CAAC,IAAIJ,eAAe,EAAE;QACtD5E,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E;IACF,CAAC,CAAC;IAEFoC,OAAO,CAAC4C,IAAI,CAAC;MACXpE,aAAa,EAAEA,aAAa,CAACqE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MAC/ClE,SAAS;MACTE,QAAQ;MACRiE,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAEjE,eAAe;MACzBkE,EAAE,EAAE,EAAE;MACNd,OAAO,EAAEA,CAAA,KAAM;QACb9D,UAAU,CAAC,KAAK,CAAC;QACjBvB,KAAK,CAACqF,OAAO,CAAC,kCAAkC,CAAC;MACnD,CAAC;MACDzE,KAAK,EAAG8B,CAAC,IAAK;QACZlB,QAAQ,CAAC,0BAA0B,IAAIkB,CAAC,CAAC6C,YAAY,IAAI,eAAe,CAAC,CAAC;QAC1EhE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6E,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,YAAY,GAAGxE,IAAI,KAAK,GAAG,GAAG,mBAAmB,GAAG,oBAAoB;IAC9ER,QAAQ,CAACgF,YAAY,CAAC;EACxB,CAAC;;EAED;EACA,MAAMR,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC3D,WAAW,EAAE;MAChBpB,OAAO,CAAC6B,IAAI,CAAC,+CAA+C,CAAC;MAC7D;IACF;IAEA,IAAI;MACF7B,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MACjF,MAAMuF,QAAQ,GAAG,MAAMrG,gBAAgB,CAAC;QAAEsG,aAAa,EAAErE;MAAY,CAAC,CAAC;MACvEpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuF,QAAQ,CAAC;MAClDtG,KAAK,CAACqF,OAAO,CAAC,6BAA6B,CAAC;;MAE5C;MACA,MAAMgB,YAAY,GAAGxE,IAAI,KAAK,GAAG,GAAG,mBAAmB,GAAG,oBAAoB;MAC9EgB,UAAU,CAAC,MAAM;QACfxB,QAAQ,CAACgF,YAAY,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAEZ,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD;MACA;MACA,MAAMyF,YAAY,GAAGxE,IAAI,KAAK,GAAG,GAAG,mBAAmB,GAAG,oBAAoB;MAC9EgB,UAAU,CAAC,MAAM;QACfxB,QAAQ,CAACgF,YAAY,CAAC;MACxB,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAED,IAAIzF,KAAK,EAAE;IACT,oBACET,OAAA;MAAKqG,SAAS,EAAC,yEAAyE;MAAAvF,QAAA,eACtFd,OAAA;QAAKqG,SAAS,EAAC,aAAa;QAAAvF,QAAA,gBAC1Bd,OAAA;UAAGqG,SAAS,EAAC,8CAA8C;UAACC,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAO;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3F3G,OAAA;UAAIqG,SAAS,EAAC,kBAAkB;UAAAvF,QAAA,EAAC;QAAa;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnD3G,OAAA;UAAGqG,SAAS,EAAC,iBAAiB;UAAAvF,QAAA,EAAEL;QAAK;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1C3G,OAAA;UAAQqG,SAAS,EAAC,iBAAiB;UAACO,OAAO,EAAEX,YAAa;UAAAnF,QAAA,EAAC;QAAO;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,OAAO,IAAI;AACb,CAAC;AAAC3F,EAAA,CAlOID,WAAW;EAAA,QACQpB,eAAe,EACrBC,WAAW;AAAA;AAAAiH,EAAA,GAFxB9F,WAAW;AAoOjB,eAAe,SAAS+F,4BAA4BA,CAAA,EAAG;EACrD,oBACE9G,OAAA,CAACC,iBAAiB;IAAAa,QAAA,eAChBd,OAAA,CAACe,WAAW;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAExB;AAACI,GAAA,GANuBD,4BAA4B;AAAA,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}